import { DateTime } from 'luxon'
import { afterFetch, BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import FarmerLoanRequirementItem from './FarmerLoanRequirementItem'
import KITA_CONFIG from 'Config/kita'

export default class FarmerLoanRequirementItemLog extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public processedById: number

  @column()
  public farmerLoanRequirementItemId: number

  @column()
  public details: string

  @column()
  public action: string

  @column()
  public attachment?: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User, {
    foreignKey: 'processedById',
  })
  public processedBy: BelongsTo<typeof User>

  @belongsTo(() => FarmerLoanRequirementItem)
  public farmerLoanRequirementItem: BelongsTo<typeof FarmerLoanRequirementItem>

  @afterFetch()
  public static afterFetchHook(items: FarmerLoanRequirementItem[]) {
    items.forEach((item) => {
      if (item.attachment) {
        const filePath = item.attachment
        const fileName = filePath.split('/')[filePath.split('/').length - 1]

        item.attachment = `${KITA_CONFIG.API_URL}/uploads/users/loanrequirements/${fileName}`
      }
    })
  }
}
