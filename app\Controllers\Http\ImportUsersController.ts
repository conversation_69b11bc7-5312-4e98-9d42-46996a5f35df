import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { triggerCreditScoreComputation } from 'App/Helpers/CreditScoring'
import { initializeFarmerLoanRequirement } from 'App/Helpers/LoanRequirement'
import CreditScoreGroup, { CreditScoreGroupStatus } from 'App/Models/CreditScoreGroup'
import Farmer from 'App/Models/Farmer'
import FarmerCreditScore from 'App/Models/FarmerCreditScore'
import FarmerCreditScoreHistory from 'App/Models/FarmerCreditScoreHistory'
import FarmerGovernmentIdentification, {
  GovernmentIdentificationType,
} from 'App/Models/FarmerGovernmentIdentification'
import FarmerInfo from 'App/Models/FarmerInfo'
import ImportUser from 'App/Models/ImportUser'
import User, { UserStatusType, UserType } from 'App/Models/User'
import UploadBulkUserValidator from 'App/Validators/UploadBulkUserValidator'

export default class ImportUsersController {
  public async upload({ auth, request, response }: HttpContextContract) {
    const { users, attachment } = await request.validate(UploadBulkUserValidator)

    if (attachment) {
      await attachment.moveToDisk('./imported/users')
    }

    for (const user of users) {
      let generatedUsername = `${user.firstName}.${user.lastName}`
        .replace(/\s/g, '')
        .replace(/-/g, '')
        .toLowerCase()

      const isExistingUsername = await User.findBy('username', generatedUsername)

      if (isExistingUsername) {
        if (user.rsbsaNumber) {
          const isExistingRSBSA = await FarmerGovernmentIdentification.query()
            .where('governmentIdType', GovernmentIdentificationType.RSBSA)
            .where('governmentIdNumber', user.rsbsaNumber)
            .whereHas('farmer', (subQuery) => {
              subQuery.where('userId', isExistingUsername.id)
            })

          if (isExistingRSBSA) {
            response.status(400)
            return response.json({
              status: 0,
              message: `User already registered with RSBSA ${user.rsbsaNumber}`,
            })
          }
        }
      }
    }

    for (const user of users) {
      let generatedUsername = `${user.firstName}.${user.lastName}`
        .replace(/\s/g, '')
        .replace(/-/g, '')
        .toLowerCase()

      const isExistingUsername = await User.findBy('username', generatedUsername)

      let suffix = 1
      let isExistingUsernameWithSuffix = await User.findBy(
        'username',
        `${generatedUsername}.${suffix}`
      )

      while (isExistingUsernameWithSuffix) {
        suffix++
        isExistingUsernameWithSuffix = await User.findBy(
          'username',
          `${generatedUsername}.${suffix}`
        )
      }

      generatedUsername = isExistingUsername ? `${generatedUsername}.${suffix}` : generatedUsername

      const createUser = await User.create({
        email: generatedUsername,
        password: 'password',
        status: UserStatusType.ACTIVATED,
        userType: UserType.FARMER,
        username: generatedUsername,
      })

      const createFarmer = await Farmer.create({
        userId: createUser.id,
        firstName: user.firstName,
        lastName: user.lastName,
        middleName: user.middleName,
        birthDate: user.birthDate,
        mobileNumber: user.mobileNumber,
        placeOfBirth: user.placeOfBirth,
        gender: user.gender,
      })

      await FarmerInfo.create({
        farmerId: createFarmer.id,
        nationality: user.nationality,
        farmArea: user.farmArea,
      })

      if (user.rsbsaNumber) {
        await FarmerGovernmentIdentification.create({
          farmerId: createFarmer.id,
          governmentIdType: GovernmentIdentificationType.RSBSA,
          governmentIdNumber: user.rsbsaNumber,
          identifier: `${createFarmer.id}-${user.rsbsaNumber}`,
        })
      }

      const getAllFinancingCompany = await CreditScoreGroup.query().where(
        'status',
        CreditScoreGroupStatus.ACTIVE
      )

      const mappedValue = getAllFinancingCompany?.map((item) => ({
        farmerId: createFarmer.id,
        creditScoreGroupId: item.id,
        loanCycleNumber: 1,
      }))

      if (mappedValue && mappedValue.length > 0) {
        await FarmerCreditScore.createMany(mappedValue)
        await FarmerCreditScoreHistory.createMany(mappedValue)
      }

      await triggerCreditScoreComputation(createUser.id)

      await initializeFarmerLoanRequirement(createFarmer.id)
    }

    await ImportUser.create({
      totalUsers: users.length,
      processedById: auth.user?.id,
      attachment: attachment?.fileName,
    })

    return response.json({
      status: 1,
      message: 'Users imported successfully',
    })
  }

  public async validate({ request, response }: HttpContextContract) {
    const { users } = await request.validate(UploadBulkUserValidator)

    let existingUsers = [] as any

    for (const user of users) {
      let generatedUsername = `${user.firstName}.${user.lastName}`
        .replace(/\s/g, '')
        .replace(/-/g, '')
        .toLowerCase()

      const isExistingUsername = await User.query()
        .preload('farmer')
        .where('username', generatedUsername)
        .first()

      if (isExistingUsername) {
        if (user.rsbsaNumber) {
          const isExistingRSBSA = await FarmerGovernmentIdentification.query()
            .where('governmentIdType', GovernmentIdentificationType.RSBSA)
            .where('governmentIdNumber', user.rsbsaNumber)
            .whereHas('farmer', (subQuery) => {
              subQuery.where('userId', isExistingUsername.id)
            })

          if (isExistingRSBSA) {
            existingUsers.push(user)
          }
        }

        if (
          isExistingUsername.farmer &&
          isExistingUsername.farmer?.birthDate?.toISODate() === user.birthDate.toISODate()
        ) {
          existingUsers.push(user)
        }
      }
    }

    if (existingUsers.length > 0) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Existing Users Detected',
        data: existingUsers,
      })
    }

    return response.json({
      status: 1,
      message: 'No existing users detected',
    })
  }

  public async viewAll({ request, response }: HttpContextContract) {
    const {
      page = 1,
      pageSize = 10,
      search,
      startDate,
      endDate,
      sortField = 'createdAt',
      sortOrder = 'desc',
    } = request.qs()

    const findImportUsers = await ImportUser.query()
      .preload('processedBy', (subQuery) => {
        subQuery.select('id', 'email', 'username').preload('admin')
      })
      .if(search, (query) => {
        query.whereHas('processedBy', (subQuery) => {
          subQuery.whereILike('email', `%${search}%`).orWhereILike('username', `%${search}%`)
        })
      })
      .if(startDate && endDate, (query) => {
        query.whereBetween('createdAt', [startDate, endDate])
      })
      .if(sortField === 'totalUsers', (query) => {
        query.orderBy('totalUsers', sortOrder === 'asc' ? 'asc' : 'desc')
      })
      .if(sortField === 'createdAt', (query) => {
        query.orderBy('createdAt', sortOrder === 'asc' ? 'asc' : 'desc')
      })
      .if(sortField === 'adminName', (query) => {
        query
          .join('users as u', 'import_users.processed_by_id', 'u.id')
          .join('admins as a', 'u.id', 'a.user_id')
          .orderBy('a.first_name', sortOrder === 'asc' ? 'asc' : 'desc')
          .select('import_users.*')
      })
      .if(!['totalUsers', 'createdAt', 'adminName'].includes(sortField), (query) => {
        query.orderBy('id', 'desc')
      })
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: findImportUsers,
    })
  }
}
