import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import AuditLog from 'App/Models/AuditLog'
import CreditScoreGroup from 'App/Models/CreditScoreGroup'
import Farmer, { HasSubmittedLoanApplication } from 'App/Models/Farmer'
import FarmerCreditScore from 'App/Models/FarmerCreditScore'
import FarmerLoanRequirement from 'App/Models/FarmerLoanRequirement'
import LoanApplicationSubmission, {
  LoanApplicationSubmissionStatus,
} from 'App/Models/LoanApplicationSubmission'
import LoanApplicationSubmissionCreditScoreGroupPivot from 'App/Models/LoanApplicationSubmissionCreditScoreGroupPivot'
import LoanPeriodTracker, { LoanPeriodTrackerStatus } from 'App/Models/LoanPeriodTracker'
import User, { UserType } from 'App/Models/User'
import CreateLoanApplicationSubmissionValidator from 'App/Validators/CreateLoanApplicationSubmissionValidator'

export default class LoanApplicationSubmissionsController {
  public async create({ auth, request, response }: HttpContextContract) {
    const { user } = auth

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid user',
      })
    }

    if (user.userType !== UserType.SUPERADMIN && user.userType !== UserType.ADMIN) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Unauthorized user!',
      })
    }

    const { userId, creditScoreGroupIds } = await request.validate(
      CreateLoanApplicationSubmissionValidator
    )

    const findCustomer = await User.find(userId)

    if (!findCustomer) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User not found!',
      })
    }

    const isExistingApprovedLoanApplication = await LoanApplicationSubmission.query()
      .where('userId', findCustomer.id)
      .where('status', LoanApplicationSubmissionStatus.APPROVE)
      .first()

    if (isExistingApprovedLoanApplication) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Latest loan application submission already approved!',
      })
    }

    const checkLastLoanTracker = await LoanPeriodTracker.query()
      .where('userId', userId)
      .andWhere('status', LoanPeriodTrackerStatus.ONGOING)
      .orderBy('id', 'desc')
      .first()

    const checkLastDoneLoanTracker = await LoanPeriodTracker.query()
      .where('userId', userId)
      .andWhere('status', LoanPeriodTrackerStatus.DONE)
      .orderBy('id', 'desc')
      .first()

    if (checkLastLoanTracker) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Loan stage is currently ongoing!',
      })
    }

    const checkFarmer = await Farmer.findBy('userId', findCustomer.id)

    if (!checkFarmer) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'No farmer found!',
      })
    }

    const getLatestFarmerCreditScore = await FarmerCreditScore.query()
      .where('farmerId', checkFarmer.id)
      .whereIn('creditScoreGroupId', creditScoreGroupIds)
      .orderBy('id', 'desc')

    const getCreditScoreGroup = await CreditScoreGroup.query().whereIn('id', creditScoreGroupIds)

    const mappedCreditGroupNames = getCreditScoreGroup.map((item) => item.name).join(', ')

    const mappedBeforeLoanScores = getCreditScoreGroup
      ?.map((item) => {
        const findScore = getLatestFarmerCreditScore.find(
          (creditScore) => creditScore.creditScoreGroupId === item.id
        )

        return findScore?.beforeStageCreditScore || 0
      })
      .join(', ')

    const findPendingStage = await FarmerLoanRequirement.query()
      .where('farmerId', checkFarmer.id)
      .whereNull('stageCompletedAt')
      .orderBy('stage', 'asc')
      .first()

    const createApplicationSubmission = await LoanApplicationSubmission.updateOrCreate(
      {
        userId,
        loanCycleNumber: checkLastDoneLoanTracker ? checkLastDoneLoanTracker.loanCyle + 1 : 1,
      },
      {
        userId,
        status: LoanApplicationSubmissionStatus.PENDING,
        loanCycleNumber: checkLastDoneLoanTracker ? checkLastDoneLoanTracker.loanCyle + 1 : 1,
        creditScoreGroups: mappedCreditGroupNames,
        beforeLoanScore: mappedBeforeLoanScores,
        action: 'Submitted',
        processedById: user.id,
        loanApplicationStage: findPendingStage?.stage || 1,
        loanApplicationCompletionCount: findPendingStage?.stageCount || 0,
      }
    )

    checkFarmer.hasSubmittedLoanApplication = HasSubmittedLoanApplication.YES

    await checkFarmer.save()

    for (const crediScoreGroup of creditScoreGroupIds) {
      await LoanApplicationSubmissionCreditScoreGroupPivot.updateOrCreate(
        {
          creditScoreGroupId: crediScoreGroup,
          loanApplicationSubmissionId: createApplicationSubmission.id,
        },
        {
          creditScoreGroupId: crediScoreGroup,
          loanApplicationSubmissionId: createApplicationSubmission.id,
        }
      )
    }

    // REMOVE UNUSED CREDIT SCORE GROUP PIVOTS
    await LoanApplicationSubmissionCreditScoreGroupPivot.query()
      .where('loanApplicationSubmissionId', createApplicationSubmission.id)
      .whereNotIn('creditScoreGroupId', creditScoreGroupIds)
      .delete()

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'CREATE ENTRY (LOAN APPLICATION SUBMISSION)',
      model: 'LoanApplicationSubmission',
      data: JSON.stringify({
        userId,
        creditScoreGroupIds,
      }),
    })

    return response.json({
      status: 1,
      message: 'Loan Application submitted successfully',
    })
  }

  public async view({ response, request }: HttpContextContract) {
    const { farmerUserId } = request.qs()

    const checkLastDoneLoanTracker = await LoanPeriodTracker.query()
      .where('userId', farmerUserId)
      .andWhere('status', LoanPeriodTrackerStatus.DONE)
      .orderBy('id', 'desc')
      .first()

    const findLoanApplicationSubmission = await LoanApplicationSubmission.query()
      .preload('financingCompanies')
      .where('userId', farmerUserId)
      .where(
        'loanCycleNumber',
        checkLastDoneLoanTracker ? checkLastDoneLoanTracker.loanCyle + 1 : 1
      )
      .orderBy('id', 'desc')
      .first()

    return response.json({
      status: 1,
      data: findLoanApplicationSubmission,
    })
  }

  public async viewAllByFarmerUserId({ response, request }: HttpContextContract) {
    const { page, pageSize } = request.qs()

    const { farmerUserId } = request.params()

    const findLoanApplicationSubmission = await LoanApplicationSubmission.query()
      .preload('processedBy', (subQuery) => {
        subQuery.preload('finance').preload('admin')
      })
      .where('userId', farmerUserId)
      .orderBy('id', 'desc')
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: findLoanApplicationSubmission,
    })
  }
}
