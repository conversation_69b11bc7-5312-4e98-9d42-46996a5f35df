/* eslint-disable @typescript-eslint/naming-convention */
import { Exception } from '@adonisjs/core/build/standalone'
import { JobContract } from '@ioc:Rocketseat/Bull'
import Crop, { CropSyncStatus } from 'App/Models/Crop'
import Ws from 'App/Services/Ws'
import KITA_CONFIG from 'Config/kita'
import axios from 'axios'
import { DateTime } from 'luxon'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessCropCloudSyncJob implements JobContract {
  public key = `${KITA_CONFIG.ENV_TYPE}-ProcessCropCloudSyncJob`

  public async handle(job) {
    const { data } = job

    try {
      const _crops = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/crop`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
        })
        .then(async (response) => response.data)

      let synced = 0

      Ws.io.to(`tradingpost-syncing`).emit(`ProcessCropCloudSyncJob`, {
        status: 1,
        data: {
          model: 'Crop',
          message: 'In Progress',
          total: _crops.data?.length,
          synced: synced,
        },
      })

      const syncIds = [] as number[]

      for (const _crop of _crops.data) {
        syncIds.push(_crop.id)

        await Crop.updateOrCreate(
          { id: _crop.id },
          {
            id: _crop.id,
            name: _crop.name,
            harvestDays: _crop.harvest_days,
            isSync: CropSyncStatus.SYNCED,
            status: _crop.status,
            createdAt: DateTime.fromISO(_crop.created_at),
            updatedAt: DateTime.fromISO(_crop.updated_at),
          }
        )

        synced++

        Ws.io.to(`tradingpost-syncing`).emit(`ProcessCropCloudSyncJob`, {
          status: 1,
          data: {
            model: 'Crop',
            message: 'In Progress',
            total: _crops.data?.length,
            synced: synced,
          },
        })
      }

      const _validateResponse = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/crop/validate`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
          syncIds,
        })
        .then(async (response) => response.data)

      console.log(_validateResponse.message)

      Ws.io.to(`tradingpost-syncing`).emit(`ProcessCropCloudSyncJob`, {
        status: 1,
        data: {
          model: 'Crop',
          message: 'Done',
          total: _crops.data?.length,
          synced: synced,
        },
      })

      return {
        data,
        result: _validateResponse.message,
        total: _crops.data?.length,
        synced: synced,
      }
    } catch (error) {
      if (error.response) {
        if (error.response.data) {
          if (error.response.data.error) {
            throw new Exception(error.response.data.error)
          }
          if (error.response.data.errors) {
            throw new Exception(error.response.data.errors.toString())
          }
        }
      } else if (error.request) {
        throw new Exception(error.request)
      } else {
        throw new Exception(error.message)
      }
    }
  }
}
