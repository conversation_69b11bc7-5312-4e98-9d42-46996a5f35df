/* eslint-disable @typescript-eslint/naming-convention */
import { Exception } from '@adonisjs/core/build/standalone'
import { JobContract } from '@ioc:Rocketseat/Bull'
import Encoder from 'App/Models/Encoder'
import Farmer from 'App/Models/Farmer'
import FarmerCropPivot from 'App/Models/FarmerCropPivot'
import FarmerVehicle from 'App/Models/FarmerVehicle'
import User, { UserSyncStatus, UserType } from 'App/Models/User'
import Ws from 'App/Services/Ws'
import KITA_CONFIG from 'Config/kita'
import axios from 'axios'
import { DateTime } from 'luxon'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessUserCloudSyncJob implements JobContract {
  public key = `${KITA_CONFIG.ENV_TYPE}-ProcessUserCloudSyncJob`

  public async handle(job) {
    const { data } = job
    try {
      const _users = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/user`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
        })
        .then(async (response) => response.data)

      let synced = 0

      Ws.io.to(`tradingpost-syncing`).emit(`ProcessUserCloudSyncJob`, {
        status: 1,
        data: {
          model: 'User',
          message: 'In Progress',
          total: _users.data?.length,
          synced: synced,
        },
      })

      const syncIds = [] as number[]

      for (const _user of _users.data) {
        syncIds.push(_user.id)

        const getFarmerDetails = _user?.farmer

        const getEncoderDetails = _user?.encoder

        const checkUser = await User.firstOrCreate(
          { email: _user.email },
          {
            email: _user.email,
            username: _user.username,
            password: 'p@$$w0rd',
            userType: _user.user_type,
            isSync: UserSyncStatus.SYNCED,
            status: _user.status,
            createdAt: DateTime.fromISO(_user.created_at),
            updatedAt: DateTime.fromISO(_user.updated_at),
            rfidNumber: _user.rfid_number,
          }
        )

        if (checkUser.userType === UserType.FARMER) {
          const checkFarmer = await Farmer.updateOrCreate(
            { userId: checkUser.id },
            {
              userId: checkUser.id,
              firstName: getFarmerDetails.first_name,
              lastName: getFarmerDetails.last_name,
              qrCode: getFarmerDetails.qr_code,
              createdAt: DateTime.fromISO(getFarmerDetails.created_at),
              updatedAt: DateTime.fromISO(getFarmerDetails.updated_at),
            }
          )

          const mappedFarmerVehicle = getFarmerDetails.farmerVehicles?.map((item) => ({
            farmerId: checkFarmer.id,
            vehiclePlateNumber: item.vehicle_plate_number,
            identifier: `${checkFarmer.id}-${item.vehicle_plate_number}`,
            createdAt: DateTime.fromISO(item.created_at),
            updatedAt: DateTime.fromISO(item.updated_at),
          }))

          if (mappedFarmerVehicle.length > 0) {
            await FarmerVehicle.createMany(mappedFarmerVehicle)
          }

          const cropsPlantedPivot = getFarmerDetails.cropsPlanted?.map((item) => ({
            cropId: item.crop_id,
            farmerId: checkFarmer.id,
            createdAt: DateTime.fromISO(_user.created_at),
            updatedAt: DateTime.fromISO(_user.updated_at),
          }))

          if (cropsPlantedPivot.length > 0) {
            await FarmerCropPivot.createMany(cropsPlantedPivot)
          }
        }

        if (
          checkUser.userType === UserType.ENCODER_1 ||
          checkUser.userType === UserType.ENCODER_2
        ) {
          await Encoder.firstOrCreate(
            {
              userId: checkUser.id,
            },
            {
              userId: checkUser.id,
              firstName: getEncoderDetails.first_name,
              lastName: getEncoderDetails.last_name,
              createdAt: DateTime.fromISO(getEncoderDetails.created_at),
              updatedAt: DateTime.fromISO(getEncoderDetails.updated_at),
            }
          )
        }

        synced++

        Ws.io.to(`tradingpost-syncing`).emit(`ProcessUserCloudSyncJob`, {
          status: 1,
          data: {
            model: 'User',
            message: 'In Progress',
            total: _users.data?.length,
            synced: synced,
          },
        })
      }

      const _validateResponse = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/user/validate`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
          syncIds,
        })
        .then(async (response) => response.data)

      console.log(_validateResponse.message)

      Ws.io.to(`tradingpost-syncing`).emit(`ProcessUserCloudSyncJob`, {
        status: 1,
        data: {
          model: 'User',
          message: 'Done',
          total: _users.data?.length,
          synced: synced,
        },
      })

      return {
        data,
        result: _validateResponse.message,
        total: _users.data?.length,
        synced: synced,
      }
    } catch (error) {
      if (error.response) {
        if (error.response.data) {
          if (error.response.data.error) {
            throw new Exception(error.response.data.error)
          }
          if (error.response.data.errors) {
            throw new Exception(error.response.data.errors.toString())
          }
        }
      } else if (error.request) {
        throw new Exception(error.request)
      } else {
        throw new Exception(error.message)
      }
    }
  }
}
