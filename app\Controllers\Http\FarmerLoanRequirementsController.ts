import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { recomputeLoanRequirement } from 'App/Helpers/LoanRequirement'
import FarmerLoanRequirement, {
  stageFourLoanRequirementTypes,
  stageOneLoanRequirementTypes,
  stageThreeLoanRequirementTypes,
  stageTwoLoanRequirementTypes,
  TFarmerLoanRequirementStage,
} from 'App/Models/FarmerLoanRequirement'
import FarmerLoanRequirementItem, {
  EFarmerLoanRequirementItemStatus,
} from 'App/Models/FarmerLoanRequirementItem'
import FarmerLoanRequirementItemLog from 'App/Models/FarmerLoanRequirementItemLog'
import UpdateFarmerLoanRequirementItemValidator from 'App/Validators/UpdateFarmerLoanRequirementItemValidator'

export default class FarmerLoanRequirementsController {
  public async viewLoanRequirements({ request, response }: HttpContextContract) {
    const { farmerId } = request.params()

    const findFarmerLoanRequirement = await FarmerLoanRequirement.query().where(
      'farmerId',
      farmerId
    )

    return response.json({
      status: 1,
      data: findFarmerLoanRequirement,
    })
  }

  public async viewLoanRequirementStage({ request, response }: HttpContextContract) {
    const { farmerId, stage } = request.params()

    const findFarmerLoanRequirement = await FarmerLoanRequirement.query()
      .preload('farmerLoanRequirementItems')
      .where('farmerId', farmerId)
      .where('stage', stage)

    return response.json({
      status: 1,
      data: findFarmerLoanRequirement,
    })
  }

  public async viewLoanRequirementItemLogs({ request, response }: HttpContextContract) {
    const { farmerLoanRequirementItemId } = request.params()

    const findFarmerLoanRequirementLog = await FarmerLoanRequirementItemLog.query()
      .where('farmerLoanRequirementItemId', farmerLoanRequirementItemId)
      .orderBy('id', 'desc')

    return response.json({
      status: 1,
      data: findFarmerLoanRequirementLog,
    })
  }

  public async updateLoanRequirementItem({ auth, request, response }: HttpContextContract) {
    const { user } = auth

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid user',
      })
    }

    const { farmerId } = request.params()

    const { name, attachment, isDone, notes } = await request.validate(
      UpdateFarmerLoanRequirementItemValidator
    )

    const findFarmerLoanRequirementItem = await FarmerLoanRequirementItem.query()
      .where('farmerId', farmerId)
      .where('name', name)
      .first()

    if (!findFarmerLoanRequirementItem) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Farmer loan requirement item not found',
      })
    }

    const allDataType = [
      ...stageOneLoanRequirementTypes,
      ...stageTwoLoanRequirementTypes,
      ...stageThreeLoanRequirementTypes,
      ...stageFourLoanRequirementTypes,
    ]

    const findDataType = allDataType.find((item) => item[0] === name)

    if (!findDataType) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid data type',
      })
    }

    if (findDataType[1] === 'conditional') {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid data type! Only checkbox and attachment types are allowed',
      })
    }

    if (
      findDataType[1] === 'attachment' &&
      !attachment &&
      isDone &&
      !findFarmerLoanRequirementItem.attachment
    ) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Attachment is required',
      })
    }

    let details = 'File'
    let action = 'Added'

    if (
      findDataType[1] === 'attachment' &&
      !findFarmerLoanRequirementItem.attachment &&
      attachment
    ) {
      details = 'File'
      action = 'Added'
    }

    if (findDataType[1] === 'attachment' && findFarmerLoanRequirementItem.attachment && !isDone) {
      details = 'File'
      action = 'Removed'
    }

    if (
      findDataType[1] === 'attachment' &&
      findFarmerLoanRequirementItem.attachment &&
      attachment &&
      isDone
    ) {
      details = 'File'
      action = 'Updated'
    }

    if (findDataType[1] === 'checkbox' && isDone) {
      details = 'Marked as done'
      action = 'Updated'
    }

    if (findDataType[1] === 'checkbox' && !isDone) {
      details = 'Marked as pending'
      action = 'Updated'
    }

    if (attachment !== undefined) {
      if (attachment && isDone) {
        await attachment.moveToDisk('./users/loanrequirements')
        findFarmerLoanRequirementItem.attachment = attachment?.fileName
      } else {
        findFarmerLoanRequirementItem.attachment = null
      }
    }

    if (notes !== undefined) {
      findFarmerLoanRequirementItem.notes = notes
    }

    if (isDone !== undefined) {
      findFarmerLoanRequirementItem.isCompleted = isDone
        ? EFarmerLoanRequirementItemStatus.COMPLETED
        : EFarmerLoanRequirementItemStatus.PENDING
    }

    await FarmerLoanRequirementItemLog.create({
      farmerLoanRequirementItemId: findFarmerLoanRequirementItem.id,
      processedById: user.id,
      details,
      action,
      attachment: findFarmerLoanRequirementItem.attachment,
    })

    await findFarmerLoanRequirementItem.save()

    await recomputeLoanRequirement(farmerId, name as TFarmerLoanRequirementStage)

    return response.json({
      status: 1,
      message: 'Farmer loan requirement item updated successfully',
    })
  }
}
