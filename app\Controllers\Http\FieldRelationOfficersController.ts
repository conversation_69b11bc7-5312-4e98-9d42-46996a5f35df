import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { triggerCreditScoreComputation } from 'App/Helpers/CreditScoring'
import {
  initializeFarmerLoanRequirement,
  triggerLoanRequirementComputation,
} from 'App/Helpers/LoanRequirement'
import AuditLog from 'App/Models/AuditLog'
import CreditScoreGroup, { CreditScoreGroupStatus } from 'App/Models/CreditScoreGroup'
import Farmer, { FarmerCivilStatus } from 'App/Models/Farmer'
import FarmerBankingDetail from 'App/Models/FarmerBankingDetail'
import FarmerCharacterReference from 'App/Models/FarmerCharacterReference'
import FarmerChemicalPivot from 'App/Models/FarmerChemicalPivot'
import FarmerCreditScore from 'App/Models/FarmerCreditScore'
import FarmerCreditScoreHistory from 'App/Models/FarmerCreditScoreHistory'
import FarmerCropPivot from 'App/Models/FarmerCropPivot'
import FarmerDataPrivacy from 'App/Models/FarmerDataPrivacy'
import FarmerFamilyProfile from 'App/Models/FarmerFamilyProfile'
import FarmerFertilizerPivot from 'App/Models/FarmerFertilizerPivot'
import FarmerGovernmentIdentification, {
  GovernmentIdentificationType,
} from 'App/Models/FarmerGovernmentIdentification'
import FarmerInfo, {
  EFertilizerUsed,
  EHasNeedFarmLoan,
  EHasPastFarmLoanPaid,
  EHasPastFarmLoans,
  EIsInterestedToSellAtTradingPost,
  EMemberOfOrganization,
  EPesticideUsed,
  FarmOwnership,
} from 'App/Models/FarmerInfo'
import FarmerInsurance from 'App/Models/FarmerInsurance'
import FarmerMobileDevice from 'App/Models/FarmerMobileDevice'
import FarmerRealProperty from 'App/Models/FarmerRealProperty'
import FarmerReferrer from 'App/Models/FarmerReferrer'
import FarmerSeedPivot from 'App/Models/FarmerSeedPivot'
import FarmerSeedSubcategoryPivot from 'App/Models/FarmerSeedSubcategoryPivot'
import FarmerSubCropPivot from 'App/Models/FarmerSubCropPivot'
import FarmerUtility from 'App/Models/FarmerUtility'
import FarmerVehicle from 'App/Models/FarmerVehicle'
import FarmerVouchLeader from 'App/Models/FarmerVouchLeader'
import FarmerVouchMao from 'App/Models/FarmerVouchMao'
import FieldRelationOfficerUser from 'App/Models/FieldRelationOfficerUser'
import User, { UserStatusType, UserSyncStatus, UserType } from 'App/Models/User'
import Wallet from 'App/Models/Wallet'
import FarmerRegistrationValidator from 'App/Validators/FarmerRegistrationValidator'
import UpdateFarmerValidator from 'App/Validators/UpdateFarmerValidator'
import { DateTime } from 'luxon'

export default class FieldRelationOfficersController {
  public async viewAllRegisteredAccounts({ request, response }: HttpContextContract) {
    const { page = 1, pageSize = 10, search } = request.qs()

    const findFarmers = await FieldRelationOfficerUser.query()
      .preload('farmerUser', (subQuery) => {
        subQuery.select('id', 'email', 'username').preload('farmer', (subQuery) => {
          subQuery.select(
            'id',
            'user_id',
            'first_name',
            'last_name',
            'middle_name',
            'address',
            'permanent_address'
          )
        })
      })
      .preload('user', (subQuery) => {
        subQuery.select('id', 'email', 'username').preload('fieldRelationOfficer', (subQuery) => {
          subQuery.select('id', 'user_id', 'first_name', 'last_name')
        })
      })
      .if(search, (query) => {
        query.whereHas('farmerUser', (subQuery) => {
          subQuery.whereILike('email', `%${search}%`).orWhereILike('username', `%${search}%`)
        })
      })
      .orderBy('id', 'desc')
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: findFarmers,
    })
  }

  public async viewAllFroRegisteredAccounts({ auth, request, response }: HttpContextContract) {
    const { user } = auth

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User not found!',
      })
    }

    const { page = 1, pageSize = 10 } = request.qs()

    const findFarmers = await FieldRelationOfficerUser.query()
      .preload('farmerUser', (subQuery) => {
        subQuery.select('id', 'email', 'username').preload('farmer', (subQuery) => {
          subQuery.select('id', 'user_id', 'first_name', 'last_name', 'middle_name')
        })
      })
      .where('user_id', user.id)
      .orderBy('id', 'desc')
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: findFarmers,
    })
  }

  public async viewFarmerByIdByFro({ auth, request, response }: HttpContextContract) {
    const { userId } = request.params()

    const { user } = auth

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User not found!',
      })
    }

    const findFarmerUser = await FieldRelationOfficerUser.query()
      .where('user_id', user.id)
      .where('farmer_user_id', userId)
      .first()

    if (!findFarmerUser) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized user!',
      })
    }

    const findUser = await User.query()
      .preload('farmer', (subQuery) => {
        subQuery
          .preload('farmerInfo')
          .preload('cropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('subCropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('familyProfiles')
          .preload('farmerInsurance')
          .preload('farmerDataPrivacy')
          .preload('farmerCreditScore')
          .preload('farmerVehicles')
          .preload('governmentIdentifications')
          .preload('realProperties')
          .preload('farmerBankDetails')
          .preload('farmerCharacterReferences')
          .preload('farmerVouchLeaders')
          .preload('farmerVouchMaos')
          .preload('farmerReferrers')
          .preload('farmerUtilities')
          .preload('farmerMobileDevice')
          .preload('chemicals', (subQuery) => {
            subQuery.preload('chemical', (subQuery) => {
              subQuery
                .preload('chemicalSubcategory')
                .preload('chemicalModeOfAction')
                .preload('chemicalActiveIngredients', (subQuery) => {
                  subQuery.preload('chemicalActiveIngredient')
                })
            })
          })
          .preload('seeds', (subQuery) => {
            subQuery.preload('seed')
          })
          .preload('seedSubcategories', (subQuery) => {
            subQuery.preload('seedSubcategory')
          })
          .preload('fertilizers', (subQuery) => {
            subQuery.preload('fertilizer')
          })
          .preload('farmerLandbankRequirements', (subQuery) => {
            subQuery.preload('processedBy')
          })
          .preload('farmerLoanRequirements')
      })
      .preload('farmPlans', (subQuery) => {
        subQuery
          .preload('crop')
          .preload('agronomistUser', (subQuery) => {
            subQuery.preload('agronomist')
          })
          .preload('user', (subQuery) => {
            subQuery.preload('farmer', (subQuery) => {
              subQuery.preload('farmerInfo')
            })
          })
          .preload('farmPlanItems', (subQuery) => {
            subQuery.preload('farmPlanSubItems', (subQuery) => {
              subQuery.preload('marketplaceProduct')
            })
          })
      })
      .preload('wallet')
      .where('userType', UserType.FARMER)
      .where('id', userId)

    return response.json({
      status: 1,
      data: findUser.length === 0 ? null : findUser[0],
    })
  }

  public async registerFarmer({ auth, request, response }: HttpContextContract) {
    const {
      email,
      password,
      firstName,
      lastName,
      middleName,
      userImage,
      birthDate,
      placeOfBirth,
      religion,
      gender,
      civilStatus,
      height,
      weight,
      mobileNumber,
      addressHouseNumber,
      addressStreet,
      addressProvince,
      addressCity,
      addressBarangay,
      addressZipCode,
      educationalAttainment,
      educationalIsGraduate,
      educationalDegree,
      occupationTitle,
      occupation,
      occupationStatus,
      occupationEmployerName,
      occupationEmployerAddress,
      occupationBusinessName,
      occupationBusinessAddress,
      occupationBusinessContact,
      occupationAnnualIncome,
      skillsFarming,
      skillsFishing,
      skillsLivestock,
      skillsConstruction,
      skillsProcessing,
      skillsServicing,
      skillsCraft,
      skillsOthers,
      governmentIdentification,
      familyProfile,
      realProperty,
      bankDetail,
      characterReference,
      vehicleOwned,
      farmAddress,
      farmArea,
      farmerVehicle,
      cropsPlanted,
      subCropsPlanted,
      seed,
      seedSubcategory,
      fertilizer,
      chemical,
      landCategory,
      crop,
      phase,
      ownerCultivator,
      tenant,
      cltEp,
      lessee,
      naturalDisasterCover,
      multiRiskCover,
      desiredAmountCover,
      additionalAmountCover,
      transplantingDate,
      harvestDate,
      sowingDate,
      seedbedding,
      planting,
      plantCare,
      insurancePremium,
      insuranceLocationPlan,
      nationality,
      yearResiding,
      residenceOwnership,
      otherMobileNumber,
      vouchByLeader,
      vouchByMao,
      referrer,
      biometric,
      mobileDeviceBrand,
      mobileDeviceModel,
      farmerUtilities,
      permanentAddressHouseNumber,
      permanentAddressStreet,
      permanentAddressProvince,
      permanentAddressCity,
      permanentAddressBarangay,
      permanentAddressZipCode,
      permanentAddressLengthOfStay,
      addressLengthOfStay,
      sourceOfFunds,
      landbankAccounts,
      priceBasedBy,
      facialRecognition,
      purchaserSellingLocation,
      purchaserFullname,
      purchaserContactNumber,
      farmAddressHouseNumber,
      farmAddressStreet,
      farmAddressProvince,
      farmAddressCity,
      farmAddressBarangay,
      farmAddressZipCode,
      farmAddressCountry,
      telephoneNumber,
      mothersMaidenName,
      facebookName,
      isAgreeUsingData,
      isAgreeSharingData,
      isAgreeVisitingFarm,
      fullName,
      attachment,
      spouseName,
      spouseMobileNumber,
      farmOwnership,
      otherFarmOwnership,
      waterSource,
      fertilizerUsed,
      pesticideUsed,
      farmImplements,
      monthlyGrossIncome,
      isMemberOfOrganization,
      organizationName,
      organizationPosition,
      hasPastFarmLoans,
      pastFarmLoans,
      hasPastFarmLoanPaid,
      hasNeedFarmLoan,
      needFarmLoanReason,
      isInterestedToSellAtTradingPost,
      signature,
      averageYieldPerYear,
    } = await request.validate(FarmerRegistrationValidator)

    if (civilStatus === FarmerCivilStatus.MARRIED) {
      if (!spouseName) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Spouse name is required',
        })
      }
    }

    let generatedUsername = `${firstName}.${lastName}`
      .replace(/\s/g, '')
      .replace(/-/g, '')
      .toLowerCase()

    const isExistingUsername = await User.findBy('username', generatedUsername)

    let suffix = 1
    let isExistingUsernameWithSuffix = await User.findBy(
      'username',
      `${generatedUsername}.${suffix}`
    )

    while (isExistingUsernameWithSuffix) {
      suffix++
      isExistingUsernameWithSuffix = await User.findBy('username', `${generatedUsername}.${suffix}`)
    }

    generatedUsername = isExistingUsername ? `${generatedUsername}.${suffix}` : generatedUsername

    if (userImage) {
      await userImage.moveToDisk('./users/profile')
    }

    if (biometric) {
      await biometric.moveToDisk('./users/biometric')
    }

    if (facialRecognition) {
      await facialRecognition.moveToDisk('./users/facial_recognition')
    }

    if (insuranceLocationPlan) {
      await insuranceLocationPlan.moveToDisk('./users/insurance')
    }

    if (attachment) {
      await attachment.moveToDisk('./users/data-privacy')
    }

    if (signature) {
      await signature.moveToDisk('./users/data-privacy')
    }

    const isExistingEmailUser = await User.findBy(
      'email',
      email ??
        `${firstName}.${lastName}.${DateTime.local().toSQLDate()}`
          .replace(/\s/g, '')
          .replace(/-/g, '')
          .toLowerCase()
    )

    if (isExistingEmailUser) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User already registered',
      })
    }

    const createUser = await User.create({
      email: email ?? generatedUsername,
      password: password ?? 'password',
      userImg: userImage?.fileName,
      userType: UserType.FARMER,
      username: generatedUsername,
      status: UserStatusType.ACTIVATED,
    })

    await Wallet.create({
      userId: createUser.id,
      credit: 0,
      balance: 0,
      payment: 0,
    })

    const qrCode = (
      new Date().getTime().toString(36) + Math.random().toString(36).slice(2)
    ).toUpperCase()

    const createFarmer = await Farmer.create({
      userId: createUser.id,
      mobileNumber,
      firstName,
      lastName,
      middleName,
      birthDate,
      placeOfBirth,
      religion,
      gender,
      civilStatus,
      spouseName: civilStatus === FarmerCivilStatus.MARRIED ? spouseName : undefined,
      spouseMobileNumber:
        civilStatus === FarmerCivilStatus.MARRIED ? spouseMobileNumber : undefined,
      height,
      weight,
      address: JSON.stringify({
        addressHouseNumber,
        addressStreet,
        addressProvince,
        addressCity,
        addressBarangay,
        addressZipCode,
      }),
      addressHouseNumber,
      addressStreet,
      addressProvince,
      addressCity,
      addressBarangay,
      addressZipCode,
      educationalAttainment,
      educationalIsGraduate,
      educationalDegree,
      occupationTitle,
      occupation,
      occupationStatus,
      occupationEmployerName,
      occupationEmployerAddress,
      occupationBusinessName,
      occupationBusinessAddress,
      occupationBusinessContact,
      occupationAnnualIncome,
      skillsFarming,
      skillsFishing,
      skillsLivestock,
      skillsConstruction,
      skillsProcessing,
      skillsServicing,
      skillsCraft,
      skillsOthers,
      vehicleOwned,
      biometric: biometric?.fileName,
      hasBiometric: biometric ? 1 : 0,
      facialRecognition: facialRecognition?.fileName,
      hasFacialRecognition: facialRecognition ? 1 : 0,
      qrCode,
      permanentAddress: JSON.stringify({
        permanentAddressHouseNumber,
        permanentAddressStreet,
        permanentAddressProvince,
        permanentAddressCity,
        permanentAddressBarangay,
        permanentAddressZipCode,
      }),
      permanentAddressHouseNumber,
      permanentAddressStreet,
      permanentAddressProvince,
      permanentAddressCity,
      permanentAddressBarangay,
      permanentAddressZipCode,
      permanentAddressLengthOfStay,
      addressLengthOfStay,
      sourceOfFunds,
      landbankAccounts,
      telephoneNumber,
      mothersMaidenName,
      facebookName,
    })

    await FarmerInfo.create({
      farmerId: createFarmer.id,
      farmAddress,
      farmAddressHouseNumber,
      farmAddressStreet,
      farmAddressProvince,
      farmAddressCity,
      farmAddressBarangay,
      farmAddressZipCode,
      farmAddressCountry,
      farmArea,
      nationality,
      yearResiding,
      residenceOwnership,
      otherMobileNumber,
      priceBasedBy,
      purchaserSellingLocation,
      purchaserFullname,
      purchaserContactNumber,
      farmOwnership,
      otherFarmOwnership,
      waterSource,
      fertilizerUsed,
      pesticideUsed,
      farmImplements,
      averageYieldPerYear,
      monthlyGrossIncome,
      isMemberOfOrganization,
      organizationName,
      organizationPosition,
      hasPastFarmLoans,
      pastFarmLoans,
      hasPastFarmLoanPaid,
      hasNeedFarmLoan,
      needFarmLoanReason,
      isInterestedToSellAtTradingPost,
    })

    await FarmerInsurance.create({
      farmerId: createFarmer.id,
      landCategory,
      crop,
      phase,
      ownerCultivator,
      tenant,
      cltEp,
      lessee,
      naturalDisasterCover,
      multiRiskCover,
      desiredAmountCover,
      additionalAmountCover,
      totalAmountCover:
        (desiredAmountCover &&
          additionalAmountCover &&
          desiredAmountCover + additionalAmountCover) ||
        0,
      transplantingDate,
      harvestDate,
      sowingDate,
      seedbedding: {
        data: seedbedding,
      },
      planting: {
        data: planting,
      },
      plantCare: {
        data: plantCare,
      },
      insurancePremium,
      insuranceLocationPlan: insuranceLocationPlan?.fileName,
    })

    await FarmerDataPrivacy.create({
      farmerId: createFarmer.id,
      isAgreeUsingData,
      isAgreeSharingData,
      isAgreeVisitingFarm,
      fullName,
      attachment: attachment?.fileName,
      signature: signature?.fileName,
    })

    if (cropsPlanted && cropsPlanted.length > 0) {
      const mappedCropsPlanted = cropsPlanted?.map((cropId) => {
        return {
          farmerId: createFarmer.id,
          cropId: cropId,
        }
      })

      await FarmerCropPivot.createMany(mappedCropsPlanted)
    }

    if (subCropsPlanted && subCropsPlanted.length > 0) {
      const mappedSubCropsPlanted = subCropsPlanted?.map((cropId) => {
        return {
          farmerId: createFarmer.id,
          cropId: cropId,
        }
      })

      await FarmerSubCropPivot.createMany(mappedSubCropsPlanted)
    }

    if (fertilizer && fertilizer.length > 0) {
      const mappedFertilizer = fertilizer?.map((fertilizerId) => {
        return {
          farmerId: createFarmer.id,
          fertilizerId: fertilizerId,
        }
      })

      await FarmerFertilizerPivot.createMany(mappedFertilizer)
    }

    if (seed && seed.length > 0) {
      const mappedSeed = seed?.map((seedId) => {
        return {
          farmerId: createFarmer.id,
          seedId: seedId,
        }
      })

      await FarmerSeedPivot.createMany(mappedSeed)
    }

    if (seedSubcategory && seedSubcategory.length > 0) {
      const mappedSeedSubcategory = seedSubcategory?.map((seedSubcategoryId) => {
        return {
          farmerId: createFarmer.id,
          seedSubcategoryId: seedSubcategoryId,
        }
      })

      await FarmerSeedSubcategoryPivot.createMany(mappedSeedSubcategory)
    }

    if (chemical && chemical.length > 0) {
      const mappedChemical = chemical?.map((chemicalId) => {
        return {
          farmerId: createFarmer.id,
          chemicalId: chemicalId,
        }
      })

      await FarmerChemicalPivot.createMany(mappedChemical)
    }

    if (familyProfile && familyProfile.length > 0) {
      const mappedFamilyProfile = familyProfile?.map((familyProfileItem) => {
        return {
          ...familyProfileItem,
          farmerId: createFarmer.id,
          isBarbazaMember: familyProfileItem.isBarbazaMember,
          isBeneficiaries: familyProfileItem.isBeneficiaries,
          identifier: `${createFarmer.id}-${familyProfileItem.name}`,
        }
      })

      await FarmerFamilyProfile.createMany(mappedFamilyProfile)
    }

    if (realProperty && realProperty.length > 0) {
      const mappedRealProperty = realProperty?.map((realPropertyItem) => {
        return {
          ...realPropertyItem,
          farmerId: createFarmer.id,
          identifier: `${createFarmer.id}-${realPropertyItem.propertyTitleNumber}`,
        }
      })

      await FarmerRealProperty.createMany(mappedRealProperty)
    }

    if (bankDetail && bankDetail.length > 0) {
      const mappedBankingDetails = bankDetail?.map((bankDetailItem) => {
        return {
          ...bankDetailItem,
          farmerId: createFarmer.id,
          identifier: `${createFarmer.id}-${bankDetailItem.bankAccountNumber}`,
        }
      })

      await FarmerBankingDetail.createMany(mappedBankingDetails)
    }

    if (characterReference && characterReference.length > 0) {
      const mappedCharacterReferences = characterReference?.map((characterReferenceItem) => {
        return {
          ...characterReferenceItem,
          farmerId: createFarmer.id,
          identifier: `${createFarmer.id}-${characterReferenceItem.name}`,
        }
      })

      await FarmerCharacterReference.createMany(mappedCharacterReferences)
    }

    if (farmerVehicle && farmerVehicle.length > 0) {
      const mappedFarmerVehicle = await Promise.all(
        farmerVehicle?.map(async (farmerVehicleItem) => {
          if (farmerVehicleItem && farmerVehicleItem.vehiclePlateNumber) {
            const entryAttachment = request.file(
              `farmerVehicle_vehicleOrcr_${farmerVehicleItem.vehiclePlateNumber}`,
              {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              }
            )

            await entryAttachment?.moveToDisk('./users/vehicleorcr')

            return {
              ...farmerVehicleItem,
              farmerId: createFarmer.id,
              vehicleOrcr: entryAttachment?.fileName ?? undefined,
              identifier: `${createFarmer.id}-${farmerVehicleItem.vehiclePlateNumber}`,
            }
          }

          return {
            ...farmerVehicleItem,
            farmerId: createFarmer.id,
            identifier: `${createFarmer.id}-${farmerVehicleItem.vehiclePlateNumber}`,
          }
        })
      )

      await FarmerVehicle.createMany(mappedFarmerVehicle)
    }

    if (governmentIdentification && governmentIdentification.length > 0) {
      const mappedGovernmentIdentification = await Promise.all(
        governmentIdentification?.map(async (governmentIdentificationItem) => {
          if (governmentIdentificationItem && governmentIdentificationItem.governmentIdNumber) {
            const entryAttachment = request.file(
              `governmentIdentification_${governmentIdentificationItem.governmentIdNumber}`,
              {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              }
            )

            if (entryAttachment) {
              await entryAttachment?.moveToDisk('./users/governmentid')

              return {
                ...governmentIdentificationItem,
                farmerId: createFarmer.id,
                governmentIdImage: entryAttachment?.fileName ?? undefined,
                identifier: `${createFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
              }
            }

            return {
              ...governmentIdentificationItem,
              farmerId: createFarmer.id,
              identifier: `${createFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
            }
          }

          return {
            ...governmentIdentificationItem,
            farmerId: createFarmer.id,
            identifier: `${createFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
          }
        })
      )

      await FarmerGovernmentIdentification.createMany(mappedGovernmentIdentification)
    }

    if (vouchByLeader && vouchByLeader.length > 0) {
      const mappedVouchByLeader = await Promise.all(
        vouchByLeader?.map(async (vouchByLeaderItem) => {
          if (vouchByLeaderItem && vouchByLeaderItem.identifier) {
            const entryAttachment = request.file(`vouchByLeader_${vouchByLeaderItem.identifier}`, {
              size: '10mb',
              extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
            })

            await entryAttachment?.moveToDisk('./users/vouchbyleader')

            return {
              ...vouchByLeaderItem,
              farmerId: createFarmer.id,
              vouchByLeadersAttachment: entryAttachment?.fileName ?? undefined,
            }
          }

          return {
            ...vouchByLeaderItem,
            farmerId: createFarmer.id,
          }
        })
      )

      await FarmerVouchLeader.createMany(mappedVouchByLeader)
    }

    if (vouchByMao && vouchByMao.length > 0) {
      const mappedVouchByMao = await Promise.all(
        vouchByMao?.map(async (vouchByMaoItem) => {
          if (vouchByMaoItem && vouchByMaoItem.identifier) {
            const entryAttachment = request.file(`vouchByMao_${vouchByMaoItem.identifier}`, {
              size: '10mb',
              extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
            })

            await entryAttachment?.moveToDisk('./users/vouchbymao')

            return {
              ...vouchByMaoItem,
              farmerId: createFarmer.id,
              vouchByMaosAttachment: entryAttachment?.fileName ?? undefined,
            }
          }

          return {
            ...vouchByMaoItem,
            farmerId: createFarmer.id,
          }
        })
      )

      await FarmerVouchMao.createMany(mappedVouchByMao)
    }

    if (referrer && referrer.length > 0) {
      const mappedReferrer = await Promise.all(
        referrer?.map(async (referrerItem) => {
          if (referrerItem && referrerItem.identifier) {
            const entryAttachment = request.file(`referrer_${referrerItem.identifier}`, {
              size: '10mb',
              extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
            })

            await entryAttachment?.moveToDisk('./users/referrer')

            return {
              ...referrerItem,
              farmerId: createFarmer.id,
              referrerAttachment: entryAttachment?.fileName ?? undefined,
            }
          }

          return {
            ...referrerItem,
            farmerId: createFarmer.id,
          }
        })
      )

      await FarmerReferrer.createMany(mappedReferrer)
    }

    if (mobileDeviceBrand || mobileDeviceModel) {
      await FarmerMobileDevice.create({
        farmerId: createFarmer.id,
        mobileDeviceBrand,
        mobileDeviceModel,
      })
    }

    if (farmerUtilities && farmerUtilities.length > 0) {
      const mappedFarmerUtilities = await Promise.all(
        farmerUtilities?.map(async (farmerUtilitiesItem) => {
          if (farmerUtilitiesItem && farmerUtilitiesItem.identifier) {
            const entryAttachment = request.file(
              `farmerUtilities_${farmerUtilitiesItem.identifier}`,
              {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              }
            )

            await entryAttachment?.moveToDisk('./users/utility')

            return {
              ...farmerUtilitiesItem,
              farmerId: createFarmer.id,
              bill: entryAttachment?.fileName ?? undefined,
            }
          }

          return {
            ...farmerUtilitiesItem,
            farmerId: createFarmer.id,
          }
        })
      )

      await FarmerUtility.createMany(mappedFarmerUtilities)
    }

    const getAllFinancingCompany = await CreditScoreGroup.query().where(
      'status',
      CreditScoreGroupStatus.ACTIVE
    )

    const mappedValue = getAllFinancingCompany?.map((item) => ({
      farmerId: createFarmer.id,
      creditScoreGroupId: item.id,
      loanCycleNumber: 1,
    }))

    if (mappedValue && mappedValue.length > 0) {
      await FarmerCreditScore.createMany(mappedValue)
      await FarmerCreditScoreHistory.createMany(mappedValue)
    }

    await triggerCreditScoreComputation(createUser.id)

    await initializeFarmerLoanRequirement(createFarmer.id)

    await FieldRelationOfficerUser.create({
      farmerUserId: createUser.id,
      userId: auth.user?.id,
    })

    return response.json({
      status: 1,
      message: 'Farmer registered successfully!',
    })
  }

  public async updateFarmer({ auth, request, response }: HttpContextContract) {
    const {
      userId,
      password,
      userImage,
      firstName,
      lastName,
      middleName,
      birthDate,
      placeOfBirth,
      religion,
      gender,
      civilStatus,
      height,
      weight,
      mobileNumber,
      addressHouseNumber,
      addressStreet,
      addressProvince,
      addressCity,
      addressBarangay,
      addressZipCode,
      educationalAttainment,
      educationalIsGraduate,
      educationalDegree,
      occupationTitle,
      occupation,
      occupationStatus,
      occupationEmployerName,
      occupationEmployerAddress,
      occupationBusinessName,
      occupationBusinessAddress,
      occupationBusinessContact,
      occupationAnnualIncome,
      skillsFarming,
      skillsFishing,
      skillsLivestock,
      skillsConstruction,
      skillsProcessing,
      skillsServicing,
      skillsCraft,
      skillsOthers,
      governmentIdentification,
      familyProfile,
      realProperty,
      bankDetail,
      characterReference,
      vehicleOwned,
      farmAddress,
      farmArea,
      farmerVehicle,
      cropsPlanted,
      subCropsPlanted,
      seed,
      seedSubcategory,
      fertilizer,
      chemical,
      landCategory,
      crop,
      phase,
      ownerCultivator,
      tenant,
      cltEp,
      lessee,
      naturalDisasterCover,
      multiRiskCover,
      desiredAmountCover,
      additionalAmountCover,
      transplantingDate,
      harvestDate,
      sowingDate,
      seedbedding,
      planting,
      plantCare,
      insurancePremium,
      insuranceLocationPlan,
      nationality,
      yearResiding,
      residenceOwnership,
      otherMobileNumber,
      vouchByLeader,
      vouchByMao,
      referrer,
      biometric,
      hasBiometric,
      mobileDeviceBrand,
      mobileDeviceModel,
      farmerUtilities,
      permanentAddressHouseNumber,
      permanentAddressStreet,
      permanentAddressProvince,
      permanentAddressCity,
      permanentAddressBarangay,
      permanentAddressZipCode,
      permanentAddressLengthOfStay,
      addressLengthOfStay,
      sourceOfFunds,
      landbankAccounts,
      priceBasedBy,
      facialRecognition,
      purchaserSellingLocation,
      purchaserFullname,
      purchaserContactNumber,
      farmAddressHouseNumber,
      farmAddressStreet,
      farmAddressProvince,
      farmAddressCity,
      farmAddressBarangay,
      farmAddressZipCode,
      farmAddressCountry,
      telephoneNumber,
      mothersMaidenName,
      facebookName,
      isAgreeUsingData,
      isAgreeSharingData,
      isAgreeVisitingFarm,
      fullName,
      attachment,
      spouseName,
      spouseMobileNumber,
      farmOwnership,
      otherFarmOwnership,
      waterSource,
      fertilizerUsed,
      pesticideUsed,
      farmImplements,
      averageYieldPerYear,
      monthlyGrossIncome,
      isMemberOfOrganization,
      organizationName,
      organizationPosition,
      hasPastFarmLoans,
      pastFarmLoans,
      hasPastFarmLoanPaid,
      hasNeedFarmLoan,
      needFarmLoanReason,
      isInterestedToSellAtTradingPost,
      signature,
      email,
    } = await request.validate(UpdateFarmerValidator)

    if (civilStatus === FarmerCivilStatus.MARRIED) {
      if (!spouseName) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Spouse name is required',
        })
      }
    }

    const findUser = await User.find(userId)

    if (!findUser) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User doesnt exist!',
      })
    }

    if (email) {
      const findUserByEmail = await User.findBy('email', email)

      if (findUserByEmail && findUserByEmail.id !== findUser.id) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Email already exists!',
        })
      }
    }

    const { user } = auth

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User not found!',
      })
    }

    const findFarmerUser = await FieldRelationOfficerUser.query()
      .where('user_id', user.id)
      .where('farmer_user_id', userId)
      .first()

    if (!findFarmerUser) {
      response.status(401)
      return response.json({
        status: 0,
        message: 'Unauthorized user!',
      })
    }

    if (userImage) {
      await userImage.moveToDisk('./users/profile')

      findUser.userImg = userImage.fileName
    }

    if (insuranceLocationPlan) {
      await insuranceLocationPlan.moveToDisk('./users/insurance')
    }

    if (biometric) {
      await biometric.moveToDisk('./users/biometric')
    }

    if (facialRecognition) {
      await facialRecognition.moveToDisk('./users/facial_recognition')
    }

    if (attachment) {
      await attachment.moveToDisk('./users/data-privacy')
    }

    if (signature) {
      await signature.moveToDisk('./users/data-privacy')
    }

    if (password) {
      findUser.password = password
    }

    const checkFarmer = await Farmer.firstOrCreate(
      {
        userId: findUser.id,
      },
      {
        userId: findUser.id,
        mobileNumber,
        firstName,
        lastName,
        middleName,
        birthDate,
        placeOfBirth,
        religion,
        gender,
        civilStatus,
        height,
        weight,
        address: JSON.stringify({
          addressHouseNumber,
          addressStreet,
          addressProvince,
          addressCity,
          addressBarangay,
          addressZipCode,
        }),
        addressHouseNumber,
        addressStreet,
        addressProvince,
        addressCity,
        addressBarangay,
        addressZipCode,
        educationalAttainment,
        educationalIsGraduate: educationalIsGraduate as number,
        educationalDegree,
        occupationTitle,
        occupation,
        occupationStatus,
        occupationEmployerName,
        occupationEmployerAddress,
        occupationBusinessName,
        occupationBusinessAddress,
        occupationBusinessContact,
        occupationAnnualIncome,
        skillsFarming,
        skillsFishing,
        skillsLivestock,
        skillsConstruction,
        skillsProcessing,
        skillsServicing,
        skillsCraft,
        skillsOthers,
        vehicleOwned,
        biometric: biometric?.fileName,
        hasBiometric: biometric ? 1 : 0,
        facialRecognition: facialRecognition?.fileName,
        hasFacialRecognition: facialRecognition ? 1 : 0,
        permanentAddress: JSON.stringify({
          permanentAddressHouseNumber,
          permanentAddressStreet,
          permanentAddressProvince,
          permanentAddressCity,
          permanentAddressBarangay,
          permanentAddressZipCode,
        }),
        permanentAddressHouseNumber,
        permanentAddressStreet,
        permanentAddressProvince,
        permanentAddressCity,
        permanentAddressBarangay,
        permanentAddressZipCode,
        permanentAddressLengthOfStay,
        addressLengthOfStay,
        sourceOfFunds,
        landbankAccounts,
        telephoneNumber,
        mothersMaidenName,
        facebookName,
      }
    )

    if (checkFarmer.$isLocal) {
      await FarmerInfo.create({
        farmerId: checkFarmer.id,
        farmAddress,
        farmAddressHouseNumber,
        farmAddressStreet,
        farmAddressProvince,
        farmAddressCity,
        farmAddressBarangay,
        farmAddressZipCode,
        farmAddressCountry,
        farmArea,
        nationality,
        yearResiding,
        residenceOwnership,
        otherMobileNumber,
        priceBasedBy,
        purchaserSellingLocation,
        purchaserFullname,
        purchaserContactNumber,
        farmOwnership: farmOwnership as FarmOwnership,
        otherFarmOwnership,
        waterSource: waterSource,
        fertilizerUsed: fertilizerUsed as EFertilizerUsed,
        pesticideUsed: pesticideUsed as EPesticideUsed,
        farmImplements: farmImplements,
        averageYieldPerYear,
        monthlyGrossIncome,
        isMemberOfOrganization,
        organizationName,
        organizationPosition,
        hasPastFarmLoans,
        pastFarmLoans,
        hasPastFarmLoanPaid,
        hasNeedFarmLoan,
        needFarmLoanReason,
        isInterestedToSellAtTradingPost,
      })

      await FarmerInsurance.create({
        farmerId: checkFarmer.id,
        landCategory,
        crop,
        phase,
        ownerCultivator,
        tenant,
        cltEp,
        lessee,
        naturalDisasterCover,
        multiRiskCover,
        desiredAmountCover,
        additionalAmountCover,
        transplantingDate,
        harvestDate,
        sowingDate,
        seedbedding: { data: seedbedding },
        planting: { data: planting },
        plantCare: { data: plantCare },
        insurancePremium,
        insuranceLocationPlan: insuranceLocationPlan?.fileName,
      })

      await FarmerDataPrivacy.create({
        farmerId: checkFarmer.id,
        isAgreeUsingData,
        isAgreeSharingData,
        isAgreeVisitingFarm,
        fullName,
        attachment: attachment?.fileName,
        signature: signature?.fileName,
      })

      if (cropsPlanted && cropsPlanted.length > 0) {
        const mappedCropsPlanted = cropsPlanted?.map((cropId) => {
          return {
            farmerId: checkFarmer.id,
            cropId: cropId,
          }
        })

        await FarmerCropPivot.createMany(mappedCropsPlanted)
      }

      if (subCropsPlanted && subCropsPlanted.length > 0) {
        const mappedSubCropsPlanted = subCropsPlanted?.map((cropId) => {
          return {
            farmerId: checkFarmer.id,
            cropId: cropId,
          }
        })

        await FarmerSubCropPivot.createMany(mappedSubCropsPlanted)
      }

      if (fertilizer && fertilizer.length > 0) {
        const mappedFertilizer = fertilizer?.map((fertilizerId) => {
          return {
            farmerId: checkFarmer.id,
            fertilizerId: fertilizerId,
          }
        })

        await FarmerFertilizerPivot.createMany(mappedFertilizer)
      }

      if (seed && seed.length > 0) {
        const mappedSeed = seed?.map((seedId) => {
          return {
            farmerId: checkFarmer.id,
            seedId: seedId,
          }
        })

        await FarmerSeedPivot.createMany(mappedSeed)
      }

      if (seedSubcategory && seedSubcategory.length > 0) {
        const mappedSeedSubcategory = seedSubcategory?.map((seedSubcategoryId) => {
          return {
            farmerId: checkFarmer.id,
            seedSubcategoryId: seedSubcategoryId,
          }
        })

        await FarmerSeedSubcategoryPivot.createMany(mappedSeedSubcategory)
      }

      if (chemical && chemical.length > 0) {
        const mappedChemical = chemical?.map((chemicalId) => {
          return {
            farmerId: checkFarmer.id,
            chemicalId: chemicalId,
          }
        })

        await FarmerChemicalPivot.createMany(mappedChemical)
      }

      if (familyProfile && familyProfile.length > 0) {
        const mappedFamilyProfile = familyProfile?.map((familyProfileItem) => {
          return {
            ...familyProfileItem,
            farmerId: checkFarmer.id,
            isBarbazaMember: familyProfileItem.isBarbazaMember,
            isBeneficiaries: familyProfileItem.isBeneficiaries,
            identifier: `${checkFarmer.id}-${familyProfileItem.name}`,
          }
        })

        await FarmerFamilyProfile.createMany(mappedFamilyProfile)
      }

      if (realProperty && realProperty.length > 0) {
        const mappedRealProperty = realProperty?.map((realPropertyItem) => {
          return {
            ...realPropertyItem,
            farmerId: checkFarmer.id,
            identifier: `${checkFarmer.id}-${realPropertyItem.propertyTitleNumber}`,
          }
        })

        await FarmerRealProperty.createMany(mappedRealProperty)
      }

      if (bankDetail && bankDetail.length > 0) {
        const mappedBankingDetail = bankDetail?.map((bankDetailItem) => {
          return {
            ...bankDetailItem,
            farmerId: checkFarmer.id,
            identifier: `${checkFarmer.id}-${bankDetailItem.bankAccountNumber}`,
          }
        })

        await FarmerBankingDetail.createMany(mappedBankingDetail)
      }

      if (characterReference && characterReference.length > 0) {
        const mappedCharacterReference = characterReference?.map((characterReferenceItem) => {
          return {
            ...characterReferenceItem,
            farmerId: checkFarmer.id,
            identifier: `${checkFarmer.id}-${characterReferenceItem.name}`,
          }
        })

        await FarmerCharacterReference.createMany(mappedCharacterReference)
      }

      if (farmerVehicle && farmerVehicle.length > 0) {
        const mappedFarmerVehicle = await Promise.all(
          farmerVehicle?.map(async (farmerVehicleItem) => {
            if (farmerVehicleItem && farmerVehicleItem.vehiclePlateNumber) {
              const entryAttachment = request.file(
                `farmerVehicle_vehicleOrcr_${farmerVehicleItem.vehiclePlateNumber}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/vehicleorcr')

              return {
                ...farmerVehicleItem,
                farmerId: checkFarmer.id,
                vehicleOrcr: entryAttachment?.fileName ?? undefined,
                identifier: `${checkFarmer.id}-${farmerVehicleItem.vehiclePlateNumber}`,
              }
            }

            return {
              ...farmerVehicleItem,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${farmerVehicleItem.vehiclePlateNumber}`,
            }
          })
        )

        await FarmerVehicle.updateOrCreateMany('identifier', mappedFarmerVehicle)

        findUser.isSync = UserSyncStatus.NOT_SYNCED
      }

      if (governmentIdentification && governmentIdentification.length > 0) {
        const mappedGovernmentIdentification = await Promise.all(
          governmentIdentification?.map(async (governmentIdentificationItem) => {
            if (governmentIdentificationItem && governmentIdentificationItem.governmentIdNumber) {
              const entryAttachment = request.file(
                `governmentIdentification_${governmentIdentificationItem.governmentIdNumber}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              if (entryAttachment) {
                await entryAttachment?.moveToDisk('./users/governmentid')

                return {
                  ...governmentIdentificationItem,
                  farmerId: checkFarmer.id,
                  governmentIdImage: entryAttachment?.fileName ?? undefined,
                  identifier: `${checkFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
                }
              }

              return {
                ...governmentIdentificationItem,
                farmerId: checkFarmer.id,
                identifier: `${checkFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
              }
            }

            return {
              ...governmentIdentificationItem,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
            }
          })
        )

        await FarmerGovernmentIdentification.createMany(mappedGovernmentIdentification)
      }

      if (vouchByLeader && vouchByLeader.length > 0) {
        const mappedVouchByLeader = await Promise.all(
          vouchByLeader?.map(async (vouchByLeaderItem) => {
            if (vouchByLeaderItem && vouchByLeaderItem.identifier) {
              const entryAttachment = request.file(
                `vouchByLeader_${vouchByLeaderItem.identifier}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/vouchbyleader')

              return {
                ...vouchByLeaderItem,
                farmerId: checkFarmer.id,
                vouchByLeadersAttachment: entryAttachment?.fileName ?? undefined,
              }
            }

            return {
              ...vouchByLeaderItem,
              farmerId: checkFarmer.id,
            }
          })
        )

        await FarmerVouchLeader.createMany(mappedVouchByLeader)
      }

      if (vouchByMao && vouchByMao.length > 0) {
        const mappedVouchByMao = await Promise.all(
          vouchByMao?.map(async (vouchByMaoItem) => {
            if (vouchByMaoItem && vouchByMaoItem.identifier) {
              const entryAttachment = request.file(`vouchByMao_${vouchByMaoItem.identifier}`, {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              })

              await entryAttachment?.moveToDisk('./users/vouchbymao')

              return {
                ...vouchByMaoItem,
                farmerId: checkFarmer.id,
                vouchByMaosAttachment: entryAttachment?.fileName ?? undefined,
              }
            }

            return {
              ...vouchByMaoItem,
              farmerId: checkFarmer.id,
            }
          })
        )

        await FarmerVouchMao.createMany(mappedVouchByMao)
      }

      if (referrer && referrer.length > 0) {
        const mappedReferrer = await Promise.all(
          referrer?.map(async (referrerItem) => {
            if (referrerItem && referrerItem.identifier) {
              const entryAttachment = request.file(`referrer_${referrerItem.identifier}`, {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              })

              await entryAttachment?.moveToDisk('./users/referrer')

              return {
                ...referrerItem,
                farmerId: checkFarmer.id,
                referrerAttachment: entryAttachment?.fileName ?? undefined,
              }
            }

            return {
              ...referrerItem,
              farmerId: checkFarmer.id,
            }
          })
        )

        await FarmerReferrer.createMany(mappedReferrer)
      }

      if (mobileDeviceBrand || mobileDeviceModel) {
        await FarmerMobileDevice.create({
          farmerId: checkFarmer.id,
          mobileDeviceBrand,
          mobileDeviceModel,
        })
      }

      if (farmerUtilities && farmerUtilities.length > 0) {
        const mappedFarmerUtilities = await Promise.all(
          farmerUtilities?.map(async (farmerUtilitiesItem) => {
            if (farmerUtilitiesItem && farmerUtilitiesItem.identifier) {
              const entryAttachment = request.file(
                `farmerUtilities_${farmerUtilitiesItem.identifier}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/utility')

              return {
                ...farmerUtilitiesItem,
                farmerId: checkFarmer.id,
                bill: entryAttachment?.fileName ?? undefined,
              }
            }

            return {
              ...farmerUtilitiesItem,
              farmerId: checkFarmer.id,
            }
          })
        )

        await FarmerUtility.createMany(mappedFarmerUtilities)
      }
    }

    if (!checkFarmer.$isLocal) {
      if (vehicleOwned !== undefined) {
        checkFarmer.vehicleOwned = vehicleOwned
      }

      if (skillsOthers !== undefined) {
        checkFarmer.skillsOthers = skillsOthers
      }

      if (skillsCraft !== undefined) {
        checkFarmer.skillsCraft = skillsCraft
      }

      if (skillsServicing !== undefined) {
        checkFarmer.skillsServicing = skillsServicing
      }

      if (skillsProcessing !== undefined) {
        checkFarmer.skillsProcessing = skillsProcessing
      }

      if (skillsConstruction !== undefined) {
        checkFarmer.skillsConstruction = skillsConstruction
      }

      if (skillsLivestock !== undefined) {
        checkFarmer.skillsLivestock = skillsLivestock
      }

      if (skillsFishing !== undefined || skillsFishing === 0) {
        checkFarmer.skillsFishing = skillsFishing || 0
      }

      if (skillsFarming !== undefined) {
        checkFarmer.skillsFarming = skillsFarming
      }

      if (sourceOfFunds !== undefined) {
        checkFarmer.sourceOfFunds = sourceOfFunds
      }

      if (landbankAccounts !== undefined) {
        checkFarmer.landbankAccounts = landbankAccounts
      }

      if (occupation || occupationAnnualIncome !== undefined || occupationAnnualIncome === 0) {
        checkFarmer.occupationAnnualIncome = occupationAnnualIncome || 0
      }

      if (occupation || occupationBusinessContact !== undefined) {
        checkFarmer.occupationBusinessContact = occupationBusinessContact
      }

      if (occupation || occupationBusinessAddress !== undefined) {
        checkFarmer.occupationBusinessAddress = occupationBusinessAddress
      }

      if (occupation || occupationBusinessContact !== undefined) {
        checkFarmer.occupationBusinessContact = occupationBusinessContact
      }

      if (occupation || occupationBusinessName !== undefined) {
        checkFarmer.occupationBusinessName = occupationBusinessName
      }

      if (occupation || occupationEmployerAddress !== undefined) {
        checkFarmer.occupationEmployerAddress = occupationEmployerAddress
      }

      if (occupation || occupationEmployerName !== undefined) {
        checkFarmer.occupationEmployerName = occupationEmployerName
      }

      if (occupation || occupationStatus !== undefined) {
        checkFarmer.occupationStatus = occupationStatus
      }

      if (occupation !== undefined) {
        checkFarmer.occupation = occupation
      }

      if (occupation || occupationTitle !== undefined) {
        checkFarmer.occupationTitle = occupationTitle
      }

      if (educationalAttainment || educationalDegree !== undefined) {
        checkFarmer.educationalDegree = educationalDegree
      }

      if (
        educationalAttainment ||
        educationalIsGraduate !== undefined ||
        educationalIsGraduate === 0
      ) {
        checkFarmer.educationalIsGraduate = educationalIsGraduate || 0
      }

      if (educationalAttainment !== undefined) {
        checkFarmer.educationalAttainment = educationalAttainment
      }

      if (addressZipCode !== undefined) {
        checkFarmer.addressZipCode = addressZipCode
      }

      if (addressCity !== undefined) {
        checkFarmer.addressCity = addressCity
      }

      if (addressBarangay !== undefined) {
        checkFarmer.addressBarangay = addressBarangay
      }

      if (addressProvince !== undefined) {
        checkFarmer.addressProvince = addressProvince
      }

      if (addressHouseNumber !== undefined) {
        checkFarmer.addressHouseNumber = addressHouseNumber
      }

      if (addressStreet !== undefined) {
        checkFarmer.addressStreet = addressStreet
      }

      checkFarmer.address = JSON.stringify({
        addressHouseNumber: checkFarmer.addressHouseNumber,
        addressStreet: checkFarmer.addressStreet,
        addressProvince: checkFarmer.addressProvince,
        addressCity: checkFarmer.addressCity,
        addressBarangay: checkFarmer.addressBarangay,
        addressZipCode: checkFarmer.addressZipCode,
      })

      if (addressLengthOfStay !== undefined || addressLengthOfStay === 0) {
        checkFarmer.addressLengthOfStay = addressLengthOfStay || 0
      }

      if (permanentAddressZipCode !== undefined) {
        checkFarmer.permanentAddressZipCode = permanentAddressZipCode
      }

      if (permanentAddressCity !== undefined) {
        checkFarmer.permanentAddressCity = permanentAddressCity
      }

      if (permanentAddressBarangay !== undefined) {
        checkFarmer.permanentAddressBarangay = permanentAddressBarangay
      }

      if (permanentAddressProvince !== undefined) {
        checkFarmer.permanentAddressProvince = permanentAddressProvince
      }

      if (permanentAddressHouseNumber !== undefined) {
        checkFarmer.permanentAddressHouseNumber = permanentAddressHouseNumber
      }

      if (permanentAddressStreet !== undefined) {
        checkFarmer.permanentAddressStreet = permanentAddressStreet
      }

      checkFarmer.permanentAddress = JSON.stringify({
        permanentAddressHouseNumber: checkFarmer.permanentAddressHouseNumber,
        permanentAddressStreet: checkFarmer.permanentAddressStreet,
        permanentAddressProvince: checkFarmer.permanentAddressProvince,
        permanentAddressCity: checkFarmer.permanentAddressCity,
        permanentAddressBarangay: checkFarmer.permanentAddressBarangay,
        permanentAddressZipCode: checkFarmer.permanentAddressZipCode,
      })

      if (permanentAddressLengthOfStay !== undefined || permanentAddressLengthOfStay === 0) {
        checkFarmer.permanentAddressLengthOfStay = permanentAddressLengthOfStay || 0
      }

      if (weight !== undefined) {
        checkFarmer.weight = weight || 0
      }

      if (height !== undefined) {
        checkFarmer.height = height || 0
      }

      if (civilStatus !== undefined) {
        checkFarmer.civilStatus = civilStatus
      }

      if (spouseName && checkFarmer.civilStatus === FarmerCivilStatus.MARRIED) {
        checkFarmer.spouseName = spouseName
      } else if (checkFarmer.civilStatus !== FarmerCivilStatus.MARRIED) {
        checkFarmer.spouseName = null
      }

      if (spouseMobileNumber && checkFarmer.civilStatus === FarmerCivilStatus.MARRIED) {
        checkFarmer.spouseMobileNumber = spouseMobileNumber
      } else if (checkFarmer.civilStatus !== FarmerCivilStatus.MARRIED) {
        checkFarmer.spouseMobileNumber = null
      }

      if (firstName !== undefined) {
        checkFarmer.firstName = firstName
      }

      if (middleName !== undefined) {
        checkFarmer.middleName = middleName
      }

      if (lastName !== undefined) {
        checkFarmer.lastName = lastName
      }

      if (birthDate !== undefined) {
        checkFarmer.birthDate = birthDate
      }

      if (placeOfBirth !== undefined) {
        checkFarmer.placeOfBirth = placeOfBirth
      }

      if (religion !== undefined) {
        checkFarmer.religion = religion
      }

      if (gender !== undefined) {
        checkFarmer.gender = gender
      }

      if (mobileNumber !== undefined) {
        checkFarmer.mobileNumber = mobileNumber
      }

      if (hasBiometric !== undefined || hasBiometric === 0) {
        checkFarmer.hasBiometric = hasBiometric || 0
      }

      if (biometric && biometric.fileName) {
        checkFarmer.biometric = biometric?.fileName
        checkFarmer.hasBiometric = 1
      }

      if (facialRecognition && facialRecognition.fileName) {
        checkFarmer.facialRecognition = facialRecognition?.fileName
        checkFarmer.hasFacialRecognition = 1
      }

      if (telephoneNumber !== undefined) {
        checkFarmer.telephoneNumber = telephoneNumber
      }

      if (mothersMaidenName !== undefined) {
        checkFarmer.mothersMaidenName = mothersMaidenName
      }

      if (facebookName !== undefined) {
        checkFarmer.facebookName = facebookName
      }

      if (cropsPlanted) {
        if (cropsPlanted.length === 0) {
          await FarmerCropPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checkCropsPlanted = await FarmerCropPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('cropId', cropsPlanted)

          const checkMappedcropsPlanted = checkCropsPlanted.map((item) => item.cropId)

          const mappedCropsPlanted = cropsPlanted
            .filter((cropId) => !checkMappedcropsPlanted.includes(cropId))
            .map((cropId) => {
              return {
                farmerId: checkFarmer.id,
                cropId: cropId,
              }
            })

          await FarmerCropPivot.createMany(mappedCropsPlanted)

          await FarmerCropPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('cropId', cropsPlanted)
            .delete()
        }
      }

      if (subCropsPlanted) {
        if (subCropsPlanted.length === 0) {
          await FarmerSubCropPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checksubCropsPlanted = await FarmerSubCropPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('cropId', subCropsPlanted)

          const checkMappedSubCropsPlanted = checksubCropsPlanted.map((item) => item.cropId)

          const mappedSubCropsPlanted = subCropsPlanted
            .filter((cropId) => !checkMappedSubCropsPlanted.includes(cropId))
            .map((cropId) => {
              return {
                farmerId: checkFarmer.id,
                cropId: cropId,
              }
            })

          await FarmerSubCropPivot.createMany(mappedSubCropsPlanted)

          await FarmerSubCropPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('cropId', subCropsPlanted)
            .delete()
        }
      }

      if (fertilizer) {
        if (fertilizer.length === 0) {
          await FarmerFertilizerPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checkfertilizer = await FarmerFertilizerPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('fertilizerId', fertilizer)

          const checkMappedfertilizer = checkfertilizer.map((item) => item.fertilizerId)

          const mappedfertilizer = fertilizer
            .filter((fertilizerId) => !checkMappedfertilizer.includes(fertilizerId))
            .map((fertilizerId) => {
              return {
                farmerId: checkFarmer.id,
                fertilizerId: fertilizerId,
              }
            })

          await FarmerFertilizerPivot.createMany(mappedfertilizer)

          await FarmerFertilizerPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('fertilizerId', fertilizer)
            .delete()
        }
      }

      if (seed) {
        if (seed.length === 0) {
          await FarmerSeedPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checkseed = await FarmerSeedPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('seedId', seed)

          const checkMappedseed = checkseed.map((item) => item.seedId)

          const mappedseed = seed
            .filter((seedId) => !checkMappedseed.includes(seedId))
            .map((seedId) => {
              return {
                farmerId: checkFarmer.id,
                seedId: seedId,
              }
            })

          await FarmerSeedPivot.createMany(mappedseed)

          await FarmerSeedPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('seedId', seed)
            .delete()
        }
      }

      if (seedSubcategory) {
        if (seedSubcategory.length === 0) {
          await FarmerSeedSubcategoryPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checkseedSubcategory = await FarmerSeedSubcategoryPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('seedSubcategoryId', seedSubcategory)

          const checkMappedseedSubcategory = checkseedSubcategory.map(
            (item) => item.seedSubcategoryId
          )

          const mappedseedSubcategory = seedSubcategory
            .filter((seedSubcategoryId) => !checkMappedseedSubcategory.includes(seedSubcategoryId))
            .map((seedSubcategoryId) => {
              return {
                farmerId: checkFarmer.id,
                seedSubcategoryId: seedSubcategoryId,
              }
            })

          await FarmerSeedSubcategoryPivot.createMany(mappedseedSubcategory)

          await FarmerSeedSubcategoryPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('seedSubcategoryId', seedSubcategory)
            .delete()
        }
      }

      if (chemical) {
        if (chemical.length === 0) {
          await FarmerChemicalPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checkchemical = await FarmerChemicalPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('chemicalId', chemical)

          const checkMappedchemical = checkchemical.map((item) => item.chemicalId)

          const mappedchemical = chemical
            .filter((chemicalId) => !checkMappedchemical.includes(chemicalId))
            .map((chemicalId) => {
              return {
                farmerId: checkFarmer.id,
                chemicalId: chemicalId,
              }
            })

          await FarmerChemicalPivot.createMany(mappedchemical)

          await FarmerChemicalPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('chemicalId', chemical)
            .delete()
        }
      }

      if (familyProfile) {
        if (familyProfile.length === 0) {
          await FarmerFamilyProfile.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const mappedFamilyProfile = familyProfile.map((item) => {
            return {
              ...item,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${item.name}`,
            }
          })

          const updateFamily = await FarmerFamilyProfile.updateOrCreateMany(
            'identifier',
            mappedFamilyProfile
          )

          const mappedUpdateFamilyId = updateFamily.map((item) => item.id)

          await FarmerFamilyProfile.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedUpdateFamilyId)
            .delete()
        }
      }

      if (realProperty) {
        if (realProperty.length === 0) {
          await FarmerRealProperty.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const mappedRealProperty = realProperty.map((item) => {
            return {
              ...item,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${item.propertyTitleNumber}`,
            }
          })

          const updateRealProperty = await FarmerRealProperty.updateOrCreateMany(
            'identifier',
            mappedRealProperty
          )

          const mappedRealPropertyId = updateRealProperty.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerRealProperty.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedRealPropertyId)
            .delete()
        }
      }

      if (bankDetail) {
        if (bankDetail.length === 0) {
          await FarmerBankingDetail.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const mappedBankingDetail = bankDetail.map((item) => {
            return {
              ...item,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${item.bankAccountNumber}`,
            }
          })

          const updateBankingDetail = await FarmerBankingDetail.updateOrCreateMany(
            'identifier',
            mappedBankingDetail
          )

          const mappedBankingDetailId = updateBankingDetail.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerBankingDetail.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedBankingDetailId)
            .delete()
        }
      }

      if (characterReference) {
        if (characterReference.length === 0) {
          await FarmerCharacterReference.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const mappedCharacterReference = characterReference.map((item) => {
            return {
              ...item,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${item.name}`,
            }
          })

          const updateCharacterReference = await FarmerCharacterReference.updateOrCreateMany(
            'identifier',
            mappedCharacterReference
          )

          const mappedCharacterReferenceId = updateCharacterReference.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerCharacterReference.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedCharacterReferenceId)
            .delete()
        }
      }

      if (farmerVehicle) {
        if (farmerVehicle.length === 0) {
          await FarmerVehicle.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpFarmerVehicle = [] as any

          // CHECK UPLOADED FILE
          for (const vehicleItem of farmerVehicle) {
            if (vehicleItem.vehiclePlateNumber) {
              const entryAttachment = request.file(
                `farmerVehicle_vehicleOrcr_${vehicleItem.vehiclePlateNumber}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/vehicleorcr')

              if (entryAttachment?.fileName) {
                tmpFarmerVehicle.push({
                  ...vehicleItem,
                  vehicleOrcr: entryAttachment?.fileName,
                  farmerId: checkFarmer.id,
                  identifier: `${checkFarmer.id}-${vehicleItem.vehiclePlateNumber}`,
                })
              } else {
                tmpFarmerVehicle.push({
                  ...vehicleItem,
                  farmerId: checkFarmer.id,
                  identifier: `${checkFarmer.id}-${vehicleItem.vehiclePlateNumber}`,
                })
              }
            }
          }

          const updateFarmerVehicle = await FarmerVehicle.updateOrCreateMany(
            'identifier',
            tmpFarmerVehicle
          )

          const mappedFarmerVehicleId = updateFarmerVehicle.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerVehicle.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedFarmerVehicleId)
            .delete()
        }

        findUser.isSync = UserSyncStatus.NOT_SYNCED
      }

      if (governmentIdentification) {
        if (governmentIdentification.length === 0) {
          await FarmerGovernmentIdentification.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpGovernmentIdentification = [] as any

          // CHECK UPLOADED FILE
          for (const governmentIdentificationItem of governmentIdentification) {
            if (governmentIdentificationItem.governmentIdNumber) {
              const entryAttachment = request.file(
                `governmentIdentification_${governmentIdentificationItem.governmentIdNumber}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              if (entryAttachment) {
                await entryAttachment?.moveToDisk('./users/governmentid')
                tmpGovernmentIdentification.push({
                  ...governmentIdentificationItem,
                  farmerId: checkFarmer.id,
                  governmentIdImage: entryAttachment?.fileName,
                  identifier: `${checkFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
                })
              } else {
                tmpGovernmentIdentification.push({
                  ...governmentIdentificationItem,
                  farmerId: checkFarmer.id,
                  identifier: `${checkFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
                })
              }
            }
          }

          const updateGovernmentIdentification =
            await FarmerGovernmentIdentification.updateOrCreateMany(
              'identifier',
              tmpGovernmentIdentification
            )

          const mappedGovernmentIdentificationId = updateGovernmentIdentification.map(
            (item) => item.id
          )

          // DELETE NON EXISTING ENTRY
          await FarmerGovernmentIdentification.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedGovernmentIdentificationId)
            .delete()
        }
      }

      if (vouchByLeader) {
        if (vouchByLeader.length === 0) {
          await FarmerVouchLeader.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpVouchLeader = [] as any

          // CHECK UPLOADED FILE
          for (const vouchByLeaderItem of vouchByLeader) {
            if (vouchByLeaderItem.identifier) {
              const entryAttachment = request.file(
                `vouchByLeader_${vouchByLeaderItem.identifier}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/vouchbyleader')

              if (entryAttachment?.fileName) {
                tmpVouchLeader.push({
                  ...vouchByLeaderItem,
                  farmerId: checkFarmer.id,
                  vouchByLeadersAttachment: entryAttachment?.fileName,
                })
              } else {
                tmpVouchLeader.push({
                  ...vouchByLeaderItem,
                  farmerId: checkFarmer.id,
                })
              }
            }
          }

          const updateVouchLeader = await FarmerVouchLeader.updateOrCreateMany(
            'identifier',
            tmpVouchLeader
          )

          const mappedVouchLeaderId = updateVouchLeader.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerVouchLeader.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedVouchLeaderId)
            .delete()
        }
      }

      if (vouchByMao) {
        if (vouchByMao.length === 0) {
          await FarmerVouchMao.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpVouchMao = [] as any

          // CHECK UPLOADED FILE
          for (const vouchByMaoItem of vouchByMao) {
            if (vouchByMaoItem.identifier) {
              const entryAttachment = request.file(`vouchByMao_${vouchByMaoItem.identifier}`, {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              })

              await entryAttachment?.moveToDisk('./users/vouchbymao')

              if (entryAttachment?.fileName) {
                tmpVouchMao.push({
                  ...vouchByMaoItem,
                  farmerId: checkFarmer.id,
                  vouchByMaosAttachment: entryAttachment?.fileName,
                })
              } else {
                tmpVouchMao.push({
                  ...vouchByMaoItem,
                  farmerId: checkFarmer.id,
                })
              }
            }
          }

          const updateVouchMao = await FarmerVouchMao.updateOrCreateMany('identifier', tmpVouchMao)

          const mappedVouchMaoId = updateVouchMao.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerVouchMao.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedVouchMaoId)
            .delete()
        }
      }

      if (referrer) {
        if (referrer.length === 0) {
          await FarmerReferrer.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpReferrer = [] as any

          // CHECK UPLOADED FILE
          for (const referrerItem of referrer) {
            if (referrerItem.identifier) {
              const entryAttachment = request.file(`referrer_${referrerItem.identifier}`, {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              })

              await entryAttachment?.moveToDisk('./users/referrer')

              if (entryAttachment?.fileName) {
                tmpReferrer.push({
                  ...referrerItem,
                  farmerId: checkFarmer.id,
                  referrerAttachment: entryAttachment?.fileName,
                })
              } else {
                tmpReferrer.push({
                  ...referrerItem,
                  farmerId: checkFarmer.id,
                })
              }
            }
          }

          const updateReferrer = await FarmerReferrer.updateOrCreateMany('identifier', tmpReferrer)

          const mappedReferrerId = updateReferrer.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerReferrer.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedReferrerId)
            .delete()
        }
      }

      if (mobileDeviceBrand || mobileDeviceModel) {
        await FarmerMobileDevice.updateOrCreate(
          {
            farmerId: checkFarmer.id,
          },
          {
            farmerId: checkFarmer.id,
            mobileDeviceBrand,
            mobileDeviceModel,
          }
        )
      }

      if (farmerUtilities) {
        if (farmerUtilities.length === 0) {
          await FarmerUtility.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpFarmerUtilities = [] as any

          // CHECK UPLOADED FILE
          for (const farmerUtilitiesItem of farmerUtilities) {
            if (farmerUtilitiesItem.identifier) {
              const entryAttachment = request.file(
                `farmerUtilities_${farmerUtilitiesItem.identifier}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/utility')

              if (entryAttachment?.fileName) {
                tmpFarmerUtilities.push({
                  ...farmerUtilitiesItem,
                  farmerId: checkFarmer.id,
                  bill: entryAttachment?.fileName,
                })
              } else {
                tmpFarmerUtilities.push({
                  ...farmerUtilitiesItem,
                  farmerId: checkFarmer.id,
                })
              }
            }
          }

          const updateFarmerUtilities = await FarmerUtility.updateOrCreateMany(
            'identifier',
            tmpFarmerUtilities
          )

          const mappedFarmerUtilitiesId = updateFarmerUtilities.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerUtility.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedFarmerUtilitiesId)
            .delete()
        }
      }
    }

    const checkFarmerInfo = await FarmerInfo.firstOrCreate(
      {
        farmerId: checkFarmer.id,
      },
      {
        farmerId: checkFarmer.id,
        farmAddress,
        farmAddressHouseNumber,
        farmAddressStreet,
        farmAddressProvince,
        farmAddressCity,
        farmAddressBarangay,
        farmAddressZipCode,
        farmAddressCountry,
        farmArea,
        nationality,
        yearResiding,
        residenceOwnership,
        otherMobileNumber,
        priceBasedBy,
        purchaserSellingLocation,
        purchaserFullname,
        purchaserContactNumber,
        farmOwnership: farmOwnership as FarmOwnership,
        otherFarmOwnership,
        waterSource: waterSource,
        fertilizerUsed: fertilizerUsed as EFertilizerUsed,
        pesticideUsed: pesticideUsed as EPesticideUsed,
        farmImplements: farmImplements,
        averageYieldPerYear,
        monthlyGrossIncome,
        isMemberOfOrganization,
        organizationName,
        organizationPosition,
        hasPastFarmLoans,
        pastFarmLoans,
        hasPastFarmLoanPaid,
        hasNeedFarmLoan,
        needFarmLoanReason,
        isInterestedToSellAtTradingPost,
      }
    )

    if (!checkFarmerInfo.$isLocal) {
      if (farmOwnership || farmOwnership === FarmOwnership.OWNED) {
        checkFarmerInfo.farmOwnership = farmOwnership as FarmOwnership
      }

      if (otherFarmOwnership && checkFarmerInfo.farmOwnership === FarmOwnership.OTHERS) {
        checkFarmerInfo.otherFarmOwnership = otherFarmOwnership
      }

      if (checkFarmerInfo.farmOwnership !== FarmOwnership.OTHERS) {
        checkFarmerInfo.otherFarmOwnership = null
      }

      if (waterSource !== undefined) {
        checkFarmerInfo.waterSource = waterSource
      }

      if (fertilizerUsed || fertilizerUsed === EFertilizerUsed.INORGANIC) {
        checkFarmerInfo.fertilizerUsed = fertilizerUsed as EFertilizerUsed
      }

      if (pesticideUsed || pesticideUsed === EPesticideUsed.INORGANIC) {
        checkFarmerInfo.pesticideUsed = pesticideUsed as EPesticideUsed
      }

      if (farmImplements !== undefined) {
        checkFarmerInfo.farmImplements = farmImplements
      }

      if (averageYieldPerYear !== undefined) {
        checkFarmerInfo.averageYieldPerYear = averageYieldPerYear || 0
      }

      if (monthlyGrossIncome !== undefined || monthlyGrossIncome === 0) {
        checkFarmerInfo.monthlyGrossIncome = monthlyGrossIncome || 0
      }

      if (isMemberOfOrganization || isMemberOfOrganization === EMemberOfOrganization.NO) {
        checkFarmerInfo.isMemberOfOrganization = isMemberOfOrganization
      }

      if (organizationName !== undefined) {
        checkFarmerInfo.organizationName = organizationName
      }

      if (organizationPosition !== undefined) {
        checkFarmerInfo.organizationPosition = organizationPosition
      }

      if (hasPastFarmLoans || hasPastFarmLoans === EHasPastFarmLoans.NO) {
        checkFarmerInfo.hasPastFarmLoans = hasPastFarmLoans
      }

      if (pastFarmLoans !== undefined) {
        checkFarmerInfo.pastFarmLoans = pastFarmLoans
      }

      if (hasPastFarmLoanPaid || hasPastFarmLoanPaid === EHasPastFarmLoanPaid.NO) {
        checkFarmerInfo.hasPastFarmLoanPaid = hasPastFarmLoanPaid
      }

      if (hasNeedFarmLoan || hasNeedFarmLoan === EHasNeedFarmLoan.NO) {
        checkFarmerInfo.hasNeedFarmLoan = hasNeedFarmLoan
      }

      if (needFarmLoanReason !== undefined) {
        checkFarmerInfo.needFarmLoanReason = needFarmLoanReason
      }

      if (
        isInterestedToSellAtTradingPost ||
        isInterestedToSellAtTradingPost === EIsInterestedToSellAtTradingPost.NO
      ) {
        checkFarmerInfo.isInterestedToSellAtTradingPost = isInterestedToSellAtTradingPost
      }

      if (farmArea !== undefined) {
        checkFarmerInfo.farmArea = farmArea || 0
      }

      if (farmAddress !== undefined) {
        checkFarmerInfo.farmAddress = farmAddress
      }

      if (farmAddressHouseNumber !== undefined) {
        checkFarmerInfo.farmAddressHouseNumber = farmAddressHouseNumber
      }

      if (farmAddressStreet !== undefined) {
        checkFarmerInfo.farmAddressStreet = farmAddressStreet
      }

      if (farmAddressProvince !== undefined) {
        checkFarmerInfo.farmAddressProvince = farmAddressProvince
      }

      if (farmAddressCity !== undefined) {
        checkFarmerInfo.farmAddressCity = farmAddressCity
      }

      if (farmAddressBarangay !== undefined) {
        checkFarmerInfo.farmAddressBarangay = farmAddressBarangay
      }

      if (farmAddressZipCode !== undefined) {
        checkFarmerInfo.farmAddressZipCode = farmAddressZipCode
      }

      if (farmAddressCountry !== undefined) {
        checkFarmerInfo.farmAddressCountry = farmAddressCountry
      }

      if (nationality !== undefined) {
        checkFarmerInfo.nationality = nationality
      }

      if (residenceOwnership !== undefined) {
        checkFarmerInfo.residenceOwnership = residenceOwnership
      }

      if (otherMobileNumber !== undefined) {
        checkFarmerInfo.otherMobileNumber = otherMobileNumber
      }

      if (yearResiding !== undefined || yearResiding === 0) {
        checkFarmerInfo.yearResiding = yearResiding || 0
      }

      if (priceBasedBy !== undefined) {
        checkFarmerInfo.priceBasedBy = priceBasedBy
      }

      if (purchaserSellingLocation !== undefined) {
        checkFarmerInfo.purchaserSellingLocation = purchaserSellingLocation
      }

      if (purchaserFullname !== undefined) {
        checkFarmerInfo.purchaserFullname = purchaserFullname
      }

      if (purchaserContactNumber !== undefined) {
        checkFarmerInfo.purchaserContactNumber = purchaserContactNumber
      }
    }

    const checkFarmerInsurance = await FarmerInsurance.firstOrCreate(
      {
        farmerId: checkFarmer.id,
      },
      {
        farmerId: checkFarmer.id,
        landCategory,
        crop,
        phase,
        ownerCultivator,
        tenant,
        cltEp,
        lessee,
        naturalDisasterCover,
        multiRiskCover,
        desiredAmountCover,
        additionalAmountCover,
        transplantingDate,
        harvestDate,
        sowingDate,
        seedbedding,
        planting,
        plantCare,
        insurancePremium,
        insuranceLocationPlan: insuranceLocationPlan?.fileName,
      }
    )

    if (!checkFarmerInsurance.$isLocal) {
      if (landCategory !== undefined) {
        checkFarmerInsurance.landCategory = landCategory
      }

      if (crop !== undefined) {
        checkFarmerInsurance.crop = crop
      }

      if (phase !== undefined) {
        checkFarmerInsurance.phase = phase
      }

      if (ownerCultivator !== undefined) {
        checkFarmerInsurance.ownerCultivator = ownerCultivator
      }

      if (tenant !== undefined) {
        checkFarmerInsurance.tenant = tenant
      }

      if (cltEp !== undefined) {
        checkFarmerInsurance.cltEp = cltEp
      }

      if (lessee !== undefined) {
        checkFarmerInsurance.lessee = lessee
      }

      if (naturalDisasterCover !== undefined) {
        checkFarmerInsurance.naturalDisasterCover = naturalDisasterCover
      }

      if (multiRiskCover !== undefined) {
        checkFarmerInsurance.multiRiskCover = multiRiskCover
      }

      if (desiredAmountCover !== undefined) {
        checkFarmerInsurance.desiredAmountCover = desiredAmountCover
      }

      if (additionalAmountCover !== undefined) {
        checkFarmerInsurance.additionalAmountCover = additionalAmountCover
      }

      if (transplantingDate !== undefined) {
        checkFarmerInsurance.transplantingDate = transplantingDate
      }

      if (harvestDate !== undefined) {
        checkFarmerInsurance.harvestDate = harvestDate
      }

      if (sowingDate !== undefined) {
        checkFarmerInsurance.sowingDate = sowingDate
      }

      if (seedbedding !== undefined) {
        checkFarmerInsurance.seedbedding = JSON.stringify({ data: seedbedding })
      }

      if (planting !== undefined) {
        checkFarmerInsurance.planting = JSON.stringify({ data: planting })
      }

      if (plantCare !== undefined) {
        checkFarmerInsurance.plantCare = JSON.stringify({ data: plantCare })
      }

      if (insurancePremium !== undefined) {
        checkFarmerInsurance.insurancePremium = insurancePremium
      }

      if (insuranceLocationPlan !== undefined) {
        checkFarmerInsurance.insuranceLocationPlan = insuranceLocationPlan?.fileName
      }
    }

    const checkFarmerDataPrivacy = await FarmerDataPrivacy.firstOrCreate(
      {
        farmerId: checkFarmer.id,
      },
      {
        farmerId: checkFarmer.id,
        isAgreeUsingData,
        isAgreeSharingData,
        isAgreeVisitingFarm,
        fullName,
        attachment: attachment?.fileName,
        signature: signature?.fileName,
      }
    )

    if (!checkFarmerDataPrivacy.$isLocal) {
      if (isAgreeUsingData || isAgreeUsingData === 0) {
        checkFarmerDataPrivacy.isAgreeUsingData = isAgreeUsingData
      }

      if (isAgreeSharingData || isAgreeSharingData === 0) {
        checkFarmerDataPrivacy.isAgreeSharingData = isAgreeSharingData
      }

      if (isAgreeVisitingFarm || isAgreeVisitingFarm === 0) {
        checkFarmerDataPrivacy.isAgreeVisitingFarm = isAgreeVisitingFarm
      }

      if (fullName !== undefined) {
        checkFarmerDataPrivacy.fullName = fullName
      }

      if (attachment !== undefined) {
        checkFarmerDataPrivacy.attachment = attachment?.fileName
      }

      if (signature !== undefined) {
        checkFarmerDataPrivacy.signature = signature?.fileName
      }
    }

    if (email !== undefined) {
      findUser.email = email
    }

    await findUser.save()

    if (!checkFarmer.$isLocal) {
      await checkFarmer.save()
    }

    if (!checkFarmerInfo.$isLocal) {
      await checkFarmerInfo.save()
    }

    if (!checkFarmerInsurance.$isLocal) {
      await checkFarmerInsurance.save()
    }

    if (!checkFarmerDataPrivacy.$isLocal) {
      await checkFarmerDataPrivacy.save()
    }

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE ENTRY (UPDATE FARMER)',
      model: 'Farmer',
    })

    await triggerCreditScoreComputation(findUser.id)

    if (userImage) {
      await triggerLoanRequirementComputation(checkFarmer.id, '2x2')
    }

    if (governmentIdentification) {
      if (governmentIdentification.length > 0) {
        await triggerLoanRequirementComputation(checkFarmer.id, 'government_id')
      }

      governmentIdentification.forEach(async (item) => {
        if (item.governmentIdType === GovernmentIdentificationType.TIN) {
          await triggerLoanRequirementComputation(checkFarmer.id, 'bir_tin')
        }

        if (item.governmentIdType === GovernmentIdentificationType.RSBSA) {
          await triggerLoanRequirementComputation(checkFarmer.id, 'rsbsa_id')
        }
      })
    }

    return response.json({
      status: 1,
      message: 'Farmer updated successfully!',
    })
  }
}
