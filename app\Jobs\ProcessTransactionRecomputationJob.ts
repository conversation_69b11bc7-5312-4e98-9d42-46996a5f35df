/* eslint-disable @typescript-eslint/naming-convention */
import { Exception } from '@adonisjs/core/build/standalone'
import { JobContract } from '@ioc:Rocketseat/Bull'
import Farmer from 'App/Models/Farmer'
import FarmerTransaction from 'App/Models/FarmerTransaction'
import FarmerTransactionHistory from 'App/Models/FarmerTransactionHistory'
import LoanPeriodTracker from 'App/Models/LoanPeriodTracker'
import MarketplaceOrder from 'App/Models/MarketplaceOrder'
import { OrderStatusType } from 'App/Models/MarketplaceOrderStatus'
import TradingPostLog, { LogStatus } from 'App/Models/TradingPostLog'
import KITA_CONFIG from 'Config/kita'
import { DateTime } from 'luxon'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessTransactionRecomputationJob implements JobContract {
  public key = `${KITA_CONFIG.ENV_TYPE}-ProcessTransactionRecomputationJob`

  public async handle(job) {
    const { data } = job
    const { farmer_id } = data
    // Do somethign with you job data
    const farmer = await Farmer.find(farmer_id)

    if (!farmer) {
      throw new Exception(`Farmer not found!`)
    }

    try {
      const findFarmerTransaction = await FarmerTransaction.firstOrCreate(
        {
          farmerId: farmer.id,
        },
        {
          farmerId: farmer.id,
          loanCycle: 1,
          beforeLoanStartAt: farmer.createdAt,
          tradingPostTotal: 0,
          tradingPostBeforeLoanTotal: 0,
          tradingPostAfterLoanTotal: 0,
          tradingPostDuringLoanTotal: 0,
          marketplaceTotal: 0,
          marketplaceAfterLoanTotal: 0,
          marketplaceBeforeLoanTotal: 0,
          marketplaceDuringLoanTotal: 0,
          salesTotal: 0,
          salesAfterLoanTotal: 0,
          salesBeforeLoanTotal: 0,
          salesDuringLoanTotal: 0,
        }
      )

      // get loan period tracker
      const getLoanTracker = await LoanPeriodTracker.query()
        .where('userId', farmer.userId)
        .orderBy('id', 'desc')

      let loanCycle = getLoanTracker.length

      if (getLoanTracker.length === 0) {
        const getAllTradingpost = await TradingPostLog.query()
          .preload('crops', (subQuery) => {
            subQuery.preload('crop')
          })
          .where('status', LogStatus.COMPLETE)
          .whereHas('farmer', (subQuery) => {
            subQuery.where('userId', farmer.userId)
          })
          .whereBetween('createdAt', [farmer.createdAt.toSQLDate()!, DateTime.local().toSQLDate()!])
          .whereNot('vehiclePlateNumber', 'MANUAL_ENTRY')
          .orderBy('id', 'desc')

        const getAllSalesHarvest = await TradingPostLog.query()
          .preload('crops', (subQuery) => {
            subQuery.preload('crop')
          })
          .where('status', LogStatus.COMPLETE)
          .whereHas('farmer', (subQuery) => {
            subQuery.where('userId', farmer.userId)
          })
          .whereBetween('createdAt', [farmer.createdAt.toSQLDate()!, DateTime.local().toSQLDate()!])
          .where('vehiclePlateNumber', 'MANUAL_ENTRY')
          .orderBy('id', 'desc')

        const getAllMarketplace = await MarketplaceOrder.query()
          .where('orderStatus', OrderStatusType.COMPLETED)
          .whereBetween('createdAt', [farmer.createdAt.toSQLDate()!, DateTime.local().toSQLDate()!])
          .where('customerId', farmer.userId)
          .orderBy('id', 'desc')

        let tmpTradingpostTransactionValue = 0

        for (const tradingpostLog of getAllTradingpost) {
          let subTotal = 0

          for (const crops of tradingpostLog.crops) {
            subTotal +=
              (tradingpostLog.entryWeight - tradingpostLog.exitWeight) *
              crops.percentage *
              crops.sellingPrice
          }

          tmpTradingpostTransactionValue += subTotal
        }

        let tmpSalesHarvestTransactionValue = 0

        for (const tradingpostLog of getAllSalesHarvest) {
          let subTotal = 0

          for (const crops of tradingpostLog.crops) {
            subTotal +=
              (tradingpostLog.entryWeight - tradingpostLog.exitWeight) *
              crops.percentage *
              crops.sellingPrice
          }

          tmpSalesHarvestTransactionValue += subTotal
        }

        let tmpMarketplaceTransactionValue = 0

        for (const marketplaceOrder of getAllMarketplace) {
          tmpMarketplaceTransactionValue += marketplaceOrder.totalPrice
        }

        findFarmerTransaction.tradingPostTotal = tmpTradingpostTransactionValue
        findFarmerTransaction.tradingPostBeforeLoanTotal = tmpTradingpostTransactionValue

        findFarmerTransaction.salesTotal = tmpSalesHarvestTransactionValue
        findFarmerTransaction.salesBeforeLoanTotal = tmpSalesHarvestTransactionValue

        findFarmerTransaction.marketplaceTotal = tmpMarketplaceTransactionValue
        findFarmerTransaction.marketplaceBeforeLoanTotal = tmpMarketplaceTransactionValue

        findFarmerTransaction.tradingPostUpdatedAt =
          getAllTradingpost.length > 0 ? getAllTradingpost[0].createdAt : null
        findFarmerTransaction.salesUpdatedAt =
          getAllSalesHarvest.length > 0 ? getAllSalesHarvest[0].createdAt : null
        findFarmerTransaction.marketplaceUpdatedAt =
          getAllMarketplace.length > 0 ? getAllMarketplace[0].createdAt : null

        await findFarmerTransaction.save()

        await FarmerTransactionHistory.updateOrCreate(
          {
            farmerId: findFarmerTransaction.farmerId,
            loanCycle,
          },
          {
            farmerId: findFarmerTransaction.farmerId,
            loanCycle,

            beforeLoanStartAt: farmer.createdAt,

            tradingPostTotal: tmpTradingpostTransactionValue,
            tradingPostBeforeLoanTotal: tmpTradingpostTransactionValue,

            salesTotal: tmpSalesHarvestTransactionValue,
            salesBeforeLoanTotal: tmpSalesHarvestTransactionValue,

            marketplaceTotal: tmpMarketplaceTransactionValue,
            marketplaceBeforeLoanTotal: tmpMarketplaceTransactionValue,

            tradingPostUpdatedAt:
              getAllTradingpost.length > 0 ? getAllTradingpost[0].createdAt : null,
            salesUpdatedAt: getAllSalesHarvest.length > 0 ? getAllSalesHarvest[0].createdAt : null,
            marketplaceUpdatedAt:
              getAllMarketplace.length > 0 ? getAllMarketplace[0].createdAt : null,
          }
        )
      } else {
        for (const loanPeriodTracker of getLoanTracker) {
          const getAllTradingpost = await TradingPostLog.query()
            .preload('crops', (subQuery) => {
              subQuery.preload('crop')
            })
            .where('status', LogStatus.COMPLETE)
            .whereHas('farmer', (subQuery) => {
              subQuery.where('userId', farmer.userId)
            })
            .whereBetween('createdAt', [
              loanPeriodTracker.beforeLoanStartAt.toSQLDate()!,
              loanPeriodTracker.duringLoanEndAt.toSQLDate()!,
            ])
            .whereNot('vehiclePlateNumber', 'MANUAL_ENTRY')
            .orderBy('id', 'desc')

          const getAllSalesHarvest = await TradingPostLog.query()
            .preload('crops', (subQuery) => {
              subQuery.preload('crop')
            })
            .where('status', LogStatus.COMPLETE)
            .whereHas('farmer', (subQuery) => {
              subQuery.where('userId', farmer.userId)
            })
            .whereBetween('createdAt', [
              loanPeriodTracker.beforeLoanStartAt.toSQLDate()!,
              loanPeriodTracker.duringLoanEndAt.toSQLDate()!,
            ])
            .where('vehiclePlateNumber', 'MANUAL_ENTRY')
            .orderBy('id', 'desc')

          const getAllMarketplace = await MarketplaceOrder.query()
            .where('orderStatus', OrderStatusType.COMPLETED)
            .whereBetween('createdAt', [
              loanPeriodTracker.beforeLoanStartAt.toSQLDate()!,
              loanPeriodTracker.duringLoanEndAt.toSQLDate()!,
            ])
            .where('customerId', farmer.userId)
            .orderBy('id', 'desc')

          // TRADINGPOST TRANSACTION
          let tmpTradingpostTransactionValue = 0
          let tmpBeforeTradingpostTransactionValue = 0
          let tmpDuringTradingpostTransactionValue = 0
          let tmpAfterTradingpostTransactionValue = 0

          for (const tradingpostLog of getAllTradingpost) {
            let subTotal = 0

            for (const crops of tradingpostLog.crops) {
              subTotal +=
                (tradingpostLog.entryWeight - tradingpostLog.exitWeight) *
                crops.percentage *
                crops.sellingPrice
            }

            tmpTradingpostTransactionValue += subTotal

            if (
              tradingpostLog.createdAt.toMillis() >=
                loanPeriodTracker.beforeLoanStartAt.toMillis() &&
              tradingpostLog.createdAt.toMillis() <= loanPeriodTracker.beforeLoanEndAt.toMillis()
            ) {
              tmpBeforeTradingpostTransactionValue += subTotal
            } else if (
              tradingpostLog.createdAt.toMillis() >=
                loanPeriodTracker.duringLoanStartAt.toMillis() &&
              tradingpostLog.createdAt.toMillis() <= loanPeriodTracker.duringLoanEndAt.toMillis()
            ) {
              tmpDuringTradingpostTransactionValue += subTotal
            } else {
              tmpAfterTradingpostTransactionValue += subTotal
            }
          }

          // SALES TRANSACTION

          let tmpSalesHarvestTransactionValue = 0
          let tmpBeforeSalesHarvestTransactionValue = 0
          let tmpDuringSalesHarvestTransactionValue = 0
          let tmpAfterSalesHarvestTransactionValue = 0

          for (const tradingpostLog of getAllSalesHarvest) {
            let subTotal = 0

            for (const crops of tradingpostLog.crops) {
              subTotal +=
                (tradingpostLog.entryWeight - tradingpostLog.exitWeight) *
                crops.percentage *
                crops.sellingPrice
            }

            tmpSalesHarvestTransactionValue += subTotal

            if (
              tradingpostLog.createdAt.toMillis() >=
                loanPeriodTracker.beforeLoanStartAt.toMillis() &&
              tradingpostLog.createdAt.toMillis() <= loanPeriodTracker.beforeLoanEndAt.toMillis()
            ) {
              tmpBeforeSalesHarvestTransactionValue += subTotal
            } else if (
              tradingpostLog.createdAt.toMillis() >=
                loanPeriodTracker.duringLoanStartAt.toMillis() &&
              tradingpostLog.createdAt.toMillis() <= loanPeriodTracker.duringLoanEndAt.toMillis()
            ) {
              tmpDuringSalesHarvestTransactionValue += subTotal
            } else {
              tmpAfterSalesHarvestTransactionValue += subTotal
            }
          }

          // MARKETPLACE TRANSACTION
          let tmpMarketplaceTransactionValue = 0
          let tmpBeforeMarketplaceTransactionValue = 0
          let tmpDuringMarketplaceTransactionValue = 0
          let tmpAfterMarketplaceTransactionValue = 0

          for (const marketplaceOrder of getAllMarketplace) {
            tmpMarketplaceTransactionValue += marketplaceOrder.totalPrice

            if (
              marketplaceOrder.createdAt.toMillis() >=
                loanPeriodTracker.beforeLoanStartAt.toMillis() &&
              marketplaceOrder.createdAt.toMillis() <= loanPeriodTracker.beforeLoanEndAt.toMillis()
            ) {
              tmpBeforeMarketplaceTransactionValue += marketplaceOrder.totalPrice
            } else if (
              marketplaceOrder.createdAt.toMillis() >=
                loanPeriodTracker.duringLoanStartAt.toMillis() &&
              marketplaceOrder.createdAt.toMillis() <= loanPeriodTracker.duringLoanEndAt.toMillis()
            ) {
              tmpDuringMarketplaceTransactionValue += marketplaceOrder.totalPrice
            } else {
              tmpAfterMarketplaceTransactionValue += marketplaceOrder.totalPrice
            }
          }

          findFarmerTransaction.loanCycle = getLoanTracker.length
          findFarmerTransaction.beforeLoanStartAt = loanPeriodTracker.beforeLoanStartAt
          findFarmerTransaction.tradingPostTotal = tmpTradingpostTransactionValue
          findFarmerTransaction.tradingPostBeforeLoanTotal = tmpBeforeTradingpostTransactionValue
          findFarmerTransaction.tradingPostDuringLoanTotal = tmpDuringTradingpostTransactionValue
          findFarmerTransaction.tradingPostAfterLoanTotal = tmpAfterTradingpostTransactionValue

          findFarmerTransaction.salesTotal = tmpSalesHarvestTransactionValue
          findFarmerTransaction.salesBeforeLoanTotal = tmpBeforeSalesHarvestTransactionValue
          findFarmerTransaction.salesDuringLoanTotal = tmpDuringSalesHarvestTransactionValue
          findFarmerTransaction.salesAfterLoanTotal = tmpAfterSalesHarvestTransactionValue

          findFarmerTransaction.marketplaceTotal = tmpMarketplaceTransactionValue
          findFarmerTransaction.marketplaceBeforeLoanTotal = tmpBeforeMarketplaceTransactionValue
          findFarmerTransaction.marketplaceDuringLoanTotal = tmpDuringMarketplaceTransactionValue
          findFarmerTransaction.marketplaceAfterLoanTotal = tmpAfterMarketplaceTransactionValue

          findFarmerTransaction.tradingPostUpdatedAt =
            getAllTradingpost.length > 0 ? getAllTradingpost[0].createdAt : null
          findFarmerTransaction.salesUpdatedAt =
            getAllSalesHarvest.length > 0 ? getAllSalesHarvest[0].createdAt : null
          findFarmerTransaction.marketplaceUpdatedAt =
            getAllMarketplace.length > 0 ? getAllMarketplace[0].createdAt : null

          await findFarmerTransaction.save()

          await FarmerTransactionHistory.updateOrCreate(
            {
              farmerId: findFarmerTransaction.farmerId,
              loanCycle,
            },
            {
              farmerId: findFarmerTransaction.farmerId,
              loanCycle,
              beforeLoanStartAt: loanPeriodTracker.beforeLoanStartAt,
              tradingPostTotal: tmpTradingpostTransactionValue,
              tradingPostBeforeLoanTotal: tmpBeforeTradingpostTransactionValue,
              tradingPostDuringLoanTotal: tmpDuringTradingpostTransactionValue,
              tradingPostAfterLoanTotal: tmpAfterTradingpostTransactionValue,

              salesTotal: tmpSalesHarvestTransactionValue,
              salesBeforeLoanTotal: tmpBeforeSalesHarvestTransactionValue,
              salesDuringLoanTotal: tmpDuringSalesHarvestTransactionValue,
              salesAfterLoanTotal: tmpAfterSalesHarvestTransactionValue,

              marketplaceTotal: tmpMarketplaceTransactionValue,
              marketplaceBeforeLoanTotal: tmpBeforeMarketplaceTransactionValue,
              marketplaceDuringLoanTotal: tmpDuringMarketplaceTransactionValue,
              marketplaceAfterLoanTotal: tmpAfterMarketplaceTransactionValue,

              tradingPostUpdatedAt:
                getAllTradingpost.length > 0 ? getAllTradingpost[0].createdAt : null,
              salesUpdatedAt:
                getAllSalesHarvest.length > 0 ? getAllSalesHarvest[0].createdAt : null,
              marketplaceUpdatedAt:
                getAllMarketplace.length > 0 ? getAllMarketplace[0].createdAt : null,
            }
          )

          loanCycle -= 1
        }
      }

      return findFarmerTransaction
    } catch (error) {
      if (error.response) {
        if (error.response.data) {
          if (error.response.data.error) {
            throw new Exception(error.response.data.error)
          }
          if (error.response.data.errors) {
            throw new Exception(error.response.data.errors.toString())
          }
        }
      } else if (error.request) {
        throw new Exception(error.request)
      } else {
        throw new Exception(error.message)
      }
    }
  }
}
