import { DateTime } from 'luxon'
import { <PERSON>Model, <PERSON><PERSON>sTo, <PERSON><PERSON><PERSON>, belongsTo, column, hasMany } from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import LoanApplicationSubmissionCreditScoreGroupPivot from './LoanApplicationSubmissionCreditScoreGroupPivot'
import CreditScoreGroup from './CreditScoreGroup'

export enum LoanApplicationSubmissionStatus {
  PENDING = 0,
  APPROVE = 1,
  REJECTED = 2,
  AVAILED = 3, //NOTE: if the farmer has topup request, topupRequestId should have value at this point (approve of topup request)
}

// TODO: Can only submit/resubmit if the status is not approve or availed
export default class LoanApplicationSubmission extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public topupRequestId: number

  @column()
  public userId: number

  @column()
  public creditScoreGroups: string

  @column()
  public action: string

  @column()
  public processedById: number

  @column()
  public approvedById: number

  @column()
  public status: LoanApplicationSubmissionStatus

  @column()
  public loanCycleNumber: number

  @column()
  public beforeLoanScore: string //snapshot before submission

  @column()
  public creditScoreGroupId: number //approved Credit Score Group

  @column()
  public loanApplicationStage: number

  @column()
  public loanApplicationCompletionCount: number

  @column.dateTime()
  public approvedAt: DateTime

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User, {
    foreignKey: 'processedById',
  })
  public processedBy: BelongsTo<typeof User>

  @belongsTo(() => User, {
    foreignKey: 'approvedById',
  })
  public approvedBy: BelongsTo<typeof User>

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @belongsTo(() => CreditScoreGroup)
  public approvedCreditScoreGroup: BelongsTo<typeof CreditScoreGroup>

  @hasMany(() => LoanApplicationSubmissionCreditScoreGroupPivot)
  public financingCompanies: HasMany<typeof LoanApplicationSubmissionCreditScoreGroupPivot>
}
