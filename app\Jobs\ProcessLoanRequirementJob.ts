/* eslint-disable @typescript-eslint/naming-convention */
import { Exception } from '@adonisjs/core/build/standalone'
import { JobContract } from '@ioc:Rocketseat/Bull'
import { recomputeLoanRequirement } from 'App/Helpers/LoanRequirement'
import KITA_CONFIG from 'Config/kita'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessLoanRequirementJob implements JobContract {
  public key = `${KITA_CONFIG.ENV_TYPE}-ProcessLoanRequirementJob`

  public async handle(job) {
    const { data } = job
    const { farmer_id, name } = data

    try {
      return await recomputeLoanRequirement(farmer_id, name)
    } catch (error) {
      if (error.response) {
        if (error.response.data) {
          if (error.response.data.error) {
            throw new Exception(error.response.data.error)
          }
          if (error.response.data.errors) {
            throw new Exception(error.response.data.errors.toString())
          }
        }
      } else if (error.request) {
        throw new Exception(error.request)
      } else {
        throw new Exception(error.message)
      }
    }
  }
}
