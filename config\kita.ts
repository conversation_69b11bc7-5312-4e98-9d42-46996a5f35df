/* eslint-disable @typescript-eslint/naming-convention */
import Env from '@ioc:Adonis/Core/Env'

export enum CONNECTION_MODE {
  LOCAL = 'LOCAL',
  CLOUD = 'CLOUD',
}

const KITA_CONFIG = {
  API_URL: Env.get('API_URL'),
  LOCAL_API_URL: Env.get('LOCAL_API_URL'),
  CONNECTION_MODE: Env.get('CONNECTION_MODE'),
  CONNECTION_SECRETKEY: Env.get('CONNECTION_SECRETKEY'),
  SHURU_SECRETKEY: Env.get('SHURU_SECRETKEY'),
  CREDIT_SCORE_API_URL: Env.get('CREDIT_SCORE_API_URL'),
  CREDIT_SCORE_API_KEY: Env.get('CREDIT_SCORE_API_KEY'),
  ENV_TYPE: Env.get('ENV_TYPE'),
}

export default KITA_CONFIG
