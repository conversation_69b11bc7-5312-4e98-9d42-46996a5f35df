/* eslint-disable @typescript-eslint/naming-convention */
import { Exception } from '@adonisjs/core/build/standalone'
import { JobContract } from '@ioc:Rocketseat/Bull'
import { recalculateDetailedCreditScore } from 'App/Helpers/CreditScoreCalculator'
import CreditScoreConfiguration from 'App/Models/CreditScoreConfiguration'
import CreditScoreGroup, { CreditScoreGroupStatus } from 'App/Models/CreditScoreGroup'
import FarmerCreditScore from 'App/Models/FarmerCreditScore'
import FarmerCreditScoreHistory from 'App/Models/FarmerCreditScoreHistory'
import LoanPeriodTracker, { LoanPeriodTrackerStatus } from 'App/Models/LoanPeriodTracker'
import User from 'App/Models/User'
import CREDIT_SCORING_CONFIG_TEMPLATE from 'Config/credit-scoring'
import KITA_CONFIG from 'Config/kita'
import { DateTime } from 'luxon'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessCreditScoreRecomputationJob implements JobContract {
  public key = `${KITA_CONFIG.ENV_TYPE}-ProcessCreditScoreRecomputationJob`

  public async handle(job) {
    const { data } = job
    const { user_id } = data

    /**
     * 1. get all done loan period tracker per user_id
     * 2. for each loan period tracker, create the credit score history
     */
    try {
      const getAllLoanPeriodTracker = await LoanPeriodTracker.query()
        .preload('topupRequest')
        .where('status', LoanPeriodTrackerStatus.DONE)
        .where('userId', user_id)
        .orderBy('id', 'asc')

      const findUser = await User.query()
        .preload('farmer', (subQuery) => {
          subQuery
            .preload('farmerInfo')
            .preload('cropsPlanted', (subQuery) => {
              subQuery.preload('crop')
            })
            .preload('user')
            .preload('familyProfiles')
            .preload('farmerInsurance')
            .preload('farmerDataPrivacy')
            .preload('farmerCreditScore')
            .preload('farmerVehicles')
            .preload('governmentIdentifications')
            .preload('realProperties')
            .preload('farmerBankDetails')
            .preload('farmerCharacterReferences')
            .preload('farmerVouchLeaders')
            .preload('farmerVouchMaos')
            .preload('chemicals', (subQuery) => {
              subQuery.preload('chemical', (subQuery) => {
                subQuery
                  .preload('chemicalSubcategory')
                  .preload('chemicalModeOfAction')
                  .preload('chemicalActiveIngredients', (subQuery) => {
                    subQuery.preload('chemicalActiveIngredient')
                  })
              })
            })
            .preload('seeds', (subQuery) => {
              subQuery.preload('seed')
            })
            .preload('fertilizers', (subQuery) => {
              subQuery.preload('fertilizer')
            })
            .preload('farmerLandbankRequirements', (subQuery) => {
              subQuery.preload('processedBy')
            })
            .preload('farmerLoanRequirements')
        })
        .where('id', user_id)
        .first()

      if (!findUser) throw new Exception('User not found!')

      const getAllCreditScoreGroup = await CreditScoreGroup.query().where(
        'status',
        CreditScoreGroupStatus.ACTIVE
      )

      for (const loanPeriodTracker of getAllLoanPeriodTracker) {
        for (const creditScoreGroup of getAllCreditScoreGroup) {
          const config = await CreditScoreConfiguration.firstOrCreate(
            {
              creditScoreGroupId: creditScoreGroup.id,
            },
            {
              creditScoreGroupId: creditScoreGroup.id,
              creditScoringConfig: CREDIT_SCORING_CONFIG_TEMPLATE,
            }
          )

          const findCreditScoreHistory = await FarmerCreditScoreHistory.firstOrCreate(
            {
              farmerId: findUser.farmer.id,
              creditScoreGroupId: creditScoreGroup.id,
              loanCycleNumber: loanPeriodTracker.loanCyle,
            },
            {
              farmerId: findUser.farmer.id,
              creditScoreGroupId: creditScoreGroup.id,
              loanCycleNumber: loanPeriodTracker.loanCyle,
            }
          )

          if (!config) throw new Exception('Credit score configuration not found!')

          const beforeResult = await recalculateDetailedCreditScore(
            findUser,
            findUser?.farmer,
            config.creditScoringConfig,
            loanPeriodTracker,
            'BEFORE'
          )

          const duringResult = await recalculateDetailedCreditScore(
            findUser,
            findUser?.farmer,
            config.creditScoringConfig,
            loanPeriodTracker,
            'DURING'
          )

          const afterResult = await recalculateDetailedCreditScore(
            findUser,
            findUser?.farmer,
            config.creditScoringConfig,
            loanPeriodTracker,
            'AFTER'
          )

          if (beforeResult) {
            findCreditScoreHistory.beforeStageCreditScoreSummary = beforeResult.stageScore
            findCreditScoreHistory.beforeStageCreditScore = beforeResult.stageScore.computedScore
          }

          if (duringResult) {
            findCreditScoreHistory.duringStageCreditScoreSummary = duringResult.stageScore
            findCreditScoreHistory.duringStageCreditScore = duringResult.stageScore.computedScore
          }

          if (afterResult) {
            findCreditScoreHistory.afterStageCreditScoreSummary = afterResult.stageScore
            findCreditScoreHistory.afterStageCreditScore = afterResult.stageScore.computedScore
          }

          if (
            loanPeriodTracker &&
            loanPeriodTracker.topupRequest.creditScoreGroupId === creditScoreGroup.id
          ) {
            findCreditScoreHistory.loanPeriodTrackerId = loanPeriodTracker.id
          }

          await findCreditScoreHistory.save()
        }
      }

      const loanPeriodTracker = await LoanPeriodTracker.query()
        .preload('topupRequest')
        .where('userId', user_id)
        .where('status', LoanPeriodTrackerStatus.ONGOING)
        .first()

      const getLastLoanPeriodTracker = await LoanPeriodTracker.query()
        .where('userId', user_id)
        .where('status', LoanPeriodTrackerStatus.DONE)
        .orderBy('id', 'desc')
        .first()

      for (const creditScoreGroup of getAllCreditScoreGroup) {
        const config = await CreditScoreConfiguration.firstOrCreate(
          {
            creditScoreGroupId: creditScoreGroup.id,
          },
          {
            creditScoreGroupId: creditScoreGroup.id,
            creditScoringConfig: CREDIT_SCORING_CONFIG_TEMPLATE,
          }
        )

        const findCreditScore = await FarmerCreditScore.firstOrCreate(
          {
            farmerId: findUser.farmer.id,
            creditScoreGroupId: creditScoreGroup.id,
          },
          {
            farmerId: findUser.farmer.id,
            creditScoreGroupId: creditScoreGroup.id,
          }
        )

        if (
          loanPeriodTracker &&
          loanPeriodTracker.topupRequest.creditScoreGroupId === creditScoreGroup.id
        ) {
          findCreditScore.loanPeriodTrackerId = loanPeriodTracker.id
        }

        findCreditScore.loanCycleNumber = loanPeriodTracker
          ? loanPeriodTracker.loanCyle
          : getLastLoanPeriodTracker
          ? getLastLoanPeriodTracker.loanCyle + 1
          : 1

        findCreditScore.loanStage = loanPeriodTracker
          ? loanPeriodTracker.afterLoanStartAt.toMillis() < DateTime.local().toMillis()
            ? 'AFTER'
            : 'DURING'
          : 'BEFORE'

        if (!config) throw new Exception('Credit score configuration not found!')

        const beforeResult = await recalculateDetailedCreditScore(
          findUser,
          findUser?.farmer,
          config.creditScoringConfig,
          loanPeriodTracker,
          'BEFORE'
        )

        if (beforeResult) {
          findCreditScore.beforeStageCreditScoreSummary = beforeResult.stageScore
          findCreditScore.beforeStageCreditScore = beforeResult.stageScore.computedScore
        }

        if (loanPeriodTracker) {
          const duringResult = await recalculateDetailedCreditScore(
            findUser,
            findUser?.farmer,
            config.creditScoringConfig,
            loanPeriodTracker,
            'DURING'
          )

          const afterResult = await recalculateDetailedCreditScore(
            findUser,
            findUser?.farmer,
            config.creditScoringConfig,
            loanPeriodTracker,
            'AFTER'
          )

          if (duringResult) {
            findCreditScore.duringStageCreditScoreSummary = duringResult.stageScore
            findCreditScore.duringStageCreditScore = duringResult.stageScore.computedScore
          }

          if (afterResult && findCreditScore.loanStage === 'AFTER') {
            findCreditScore.afterStageCreditScoreSummary = afterResult.stageScore
            findCreditScore.afterStageCreditScore = afterResult.stageScore.computedScore
          }
        }

        await findCreditScore.save()

        const findCreditScoreHistory = await FarmerCreditScoreHistory.firstOrCreate(
          {
            farmerId: findUser.farmer.id,
            creditScoreGroupId: creditScoreGroup.id,
            loanCycleNumber: findCreditScore.loanCycleNumber,
          },
          {
            farmerId: findUser.farmer.id,
            creditScoreGroupId: creditScoreGroup.id,
            loanCycleNumber: findCreditScore.loanCycleNumber,
          }
        )

        if (
          loanPeriodTracker &&
          loanPeriodTracker.topupRequest.creditScoreGroupId === creditScoreGroup.id
        ) {
          findCreditScoreHistory.loanPeriodTrackerId = loanPeriodTracker.id
        }

        findCreditScoreHistory.beforeStageCreditScoreSummary =
          findCreditScore.beforeStageCreditScoreSummary
        findCreditScoreHistory.beforeStageCreditScore = findCreditScore.beforeStageCreditScore

        findCreditScoreHistory.duringStageCreditScoreSummary =
          findCreditScore.duringStageCreditScoreSummary
        findCreditScoreHistory.duringStageCreditScore = findCreditScore.duringStageCreditScore

        findCreditScoreHistory.afterStageCreditScoreSummary =
          findCreditScore.afterStageCreditScoreSummary
        findCreditScoreHistory.afterStageCreditScore = findCreditScore.afterStageCreditScore

        await findCreditScoreHistory.save()
      }
    } catch (error) {
      if (error.response) {
        if (error.response.data) {
          if (error.response.data.error) {
            throw new Exception(error.response.data.error)
          }
          if (error.response.data.errors) {
            throw new Exception(error.response.data.errors.toString())
          }
        }
      } else if (error.request) {
        throw new Exception(error.request)
      } else {
        throw new Exception(error.message)
      }
    }
  }
}
