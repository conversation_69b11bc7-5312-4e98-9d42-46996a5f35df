import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Bull from '@ioc:Rocketseat/Bull'
import ProcessCreditScoreRecomputationJob from 'App/Jobs/ProcessCreditScoreRecomputationJob'
import ProcessInitializeLoanRequirementJob from 'App/Jobs/ProcessInitializeLoanRequirementJob'
import ProcessLoanPeriodTrackerJob from 'App/Jobs/ProcessLoanPeriodTrackerJob'
import ProcessTransactionRecomputationJob from 'App/Jobs/ProcessTransactionRecomputationJob'
import Farmer from 'App/Models/Farmer'
import KITA_CONFIG from 'Config/kita'

export default class MiscellaneousController {
  public async initiateTransactionRecomputationAllFarmer({ response }: HttpContextContract) {
    const findFarmers = await Farmer.all()

    for (const farmer of findFarmers) {
      await Bull.add(
        new ProcessTransactionRecomputationJob().key,
        {
          farmer_id: farmer.id,
        },
        {
          attempts: 5,
        }
      )
    }

    return response.json({
      status: 1,
      message: 'Transaction recomputation triggered!',
    })
  }

  public async initiateCreditScoreSyncAllFarmer({ response }: HttpContextContract) {
    return response.json({
      status: 1,
      message: 'Credit score syncing triggered!',
    })
  }

  public async initiateLoanPeriodTrackerSyncAllFarmer({ response }: HttpContextContract) {
    await Bull.add(
      new ProcessLoanPeriodTrackerJob().key,
      {},
      {
        attempts: 5,
      }
    )

    return response.json({
      status: 1,
      message: 'Loan period tracker syncing triggered!',
    })
  }

  public async creditScoreCallback({ response, request }: HttpContextContract) {
    if (request.headers().authorization !== KITA_CONFIG.CREDIT_SCORE_API_KEY) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid token!',
      })
    }

    return response.json({
      status: 1,
      message: 'Success!',
    })
  }

  public async singleCreditScoreRecomputation({ response, request }: HttpContextContract) {
    const { farmerId } = request.params()

    const findFarmer = await Farmer.query().where('id', farmerId).first()

    if (!findFarmer) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Farmer not found!',
      })
    }

    await Bull.add(
      new ProcessCreditScoreRecomputationJob().key,
      {
        user_id: findFarmer.userId,
      },
      {
        attempts: 5,
      }
    )

    return response.json({
      status: 1,
      message: 'Credit score recomputation triggered!',
    })
  }

  public async allCreditScoreRecomputation({ response }: HttpContextContract) {
    const findFarmers = await Farmer.all()

    for (const farmer of findFarmers) {
      await Bull.add(
        new ProcessCreditScoreRecomputationJob().key,
        {
          user_id: farmer.userId,
        },
        {
          attempts: 5,
        }
      )
    }

    return response.json({
      status: 1,
      message: 'Credit score recomputation triggered!',
    })
  }

  public async initiateLoanRequirementComputationAllFarmer({ response }: HttpContextContract) {
    const findFarmers = await Farmer.all()

    for (const farmer of findFarmers) {
      await Bull.add(
        new ProcessInitializeLoanRequirementJob().key,
        {
          farmer_id: farmer.id,
        },
        {
          attempts: 5,
        }
      )
    }

    return response.json({
      status: 1,
      message: 'Loan requirement computation triggered!',
    })
  }
}
