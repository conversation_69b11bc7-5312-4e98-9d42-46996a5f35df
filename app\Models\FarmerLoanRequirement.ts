import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'
import Farmer from './Farmer'
import FarmerLoanRequirementItem from './FarmerLoanRequirementItem'

export type TFarmerLoanRequirementItemType = 'attachment' | 'checkbox' | 'conditional'

export enum EFarmerLoanRequirementItemStage {
  STAGE_ONE = 1,
  STAGE_TWO = 2,
  STAGE_THREE = 3,
  STAGE_FOUR = 4,
}

export type TFarmerLoanRequirementStageOne =
  | '2x2'
  | 'bir_tin'
  | 'rsbsa_id'
  | 'government_id'
  | 'barangay_clearance'
  | 'land_agreement'
  | 'kita_loan_form'
  | 'lbp_agrisenso_loan_form'
  | 'lbp_data_privacy'
  | 'farmer_meeting'

export type TFarmerLoanRequirementStageTwo =
  | 'nia_certification'
  | 'ia_certification'
  | 'dar_certification'
  | 'mao_certification'
  | 'pcic'
  | 'ptma'
  | 'msa'
  | 'farm_plan'
  | 'tripartite_agreement'
  | 'signed_notarized_documents'

export type TFarmerLoanRequirementStageThree =
  | 'submission_of_documents'
  | 'verification_of_documents'
  | 'promissory_note_signing'
  | 'opening_of_account'

export type TFarmerLoanRequirementStageFour =
  | 'agri_inputs_distribution'
  | 'submission_of_soa'
  | 'transfer_of_cash'
  | 'amount_to_be_credited'

export type TFarmerLoanRequirementStage =
  | TFarmerLoanRequirementStageOne
  | TFarmerLoanRequirementStageTwo
  | TFarmerLoanRequirementStageThree
  | TFarmerLoanRequirementStageFour

export const stageOneLoanRequirements: TFarmerLoanRequirementStageOne[] = [
  '2x2',
  'bir_tin',
  'rsbsa_id',
  'government_id',
  'barangay_clearance',
  'land_agreement',
  'kita_loan_form',
  'lbp_agrisenso_loan_form',
  'lbp_data_privacy',
  'farmer_meeting',
]

export const stageOneLoanRequirementTypes: [
  TFarmerLoanRequirementStageOne,
  TFarmerLoanRequirementItemType,
][] = [
  ['2x2', 'conditional'],
  ['bir_tin', 'conditional'],
  ['rsbsa_id', 'conditional'],
  ['government_id', 'conditional'],
  ['barangay_clearance', 'attachment'],
  ['land_agreement', 'attachment'],
  ['kita_loan_form', 'attachment'],
  ['lbp_agrisenso_loan_form', 'attachment'],
  ['lbp_data_privacy', 'attachment'],
  ['farmer_meeting', 'checkbox'],
]

export const stageTwoLoanRequirements: TFarmerLoanRequirementStageTwo[] = [
  'nia_certification',
  'ia_certification',
  'dar_certification',
  'mao_certification',
  'pcic',
  'ptma',
  'msa',
  'farm_plan',
  'tripartite_agreement',
  'signed_notarized_documents',
]

export const stageTwoLoanRequirementTypes: [
  TFarmerLoanRequirementStageTwo,
  TFarmerLoanRequirementItemType,
][] = [
  ['nia_certification', 'attachment'],
  ['ia_certification', 'attachment'],
  ['dar_certification', 'attachment'],
  ['mao_certification', 'attachment'],
  ['pcic', 'checkbox'],
  ['ptma', 'attachment'],
  ['msa', 'attachment'],
  ['farm_plan', 'attachment'],
  ['tripartite_agreement', 'attachment'],
  ['signed_notarized_documents', 'checkbox'],
]

export const stageThreeLoanRequirements: TFarmerLoanRequirementStageThree[] = [
  'submission_of_documents',
  'verification_of_documents',
  'promissory_note_signing',
  'opening_of_account',
]

export const stageThreeLoanRequirementTypes: [
  TFarmerLoanRequirementStageThree,
  TFarmerLoanRequirementItemType,
][] = [
  ['submission_of_documents', 'checkbox'],
  ['verification_of_documents', 'checkbox'],
  ['promissory_note_signing', 'checkbox'],
  ['opening_of_account', 'checkbox'],
]

export const stageFourLoanRequirements: TFarmerLoanRequirementStageFour[] = [
  'agri_inputs_distribution',
  'submission_of_soa',
  'transfer_of_cash',
  'amount_to_be_credited',
]

export const stageFourLoanRequirementTypes: [
  TFarmerLoanRequirementStageFour,
  TFarmerLoanRequirementItemType,
][] = [
  ['agri_inputs_distribution', 'checkbox'],
  ['submission_of_soa', 'checkbox'],
  ['transfer_of_cash', 'checkbox'],
  ['amount_to_be_credited', 'checkbox'],
]

export default class FarmerLoanRequirement extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public farmerId: number

  @column()
  public stage: EFarmerLoanRequirementItemStage

  @column({
    prepare: (value: any) => JSON.stringify(value),
    consume: (value: any) => value && (typeof value === 'string' ? JSON.parse(value) : value),
  })
  public stageSummary: Record<TFarmerLoanRequirementStage, boolean>

  @column()
  public stageCount: number

  @column.dateTime()
  public stageStartedAt: DateTime

  @column.dateTime()
  public stageCompletedAt: DateTime | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Farmer)
  public farmer: BelongsTo<typeof Farmer>

  @hasMany(() => FarmerLoanRequirementItem)
  public farmerLoanRequirementItems: HasMany<typeof FarmerLoanRequirementItem>
}
