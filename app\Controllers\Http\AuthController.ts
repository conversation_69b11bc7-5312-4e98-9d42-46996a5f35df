import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { triggerCreditScoreComputation } from 'App/Helpers/CreditScoring'
import { initializeFarmerLoanRequirement } from 'App/Helpers/LoanRequirement'
import AuditLog from 'App/Models/AuditLog'
import CreditScoreGroup, { CreditScoreGroupStatus } from 'App/Models/CreditScoreGroup'
import Farmer from 'App/Models/Farmer'
import FarmerBankingDetail from 'App/Models/FarmerBankingDetail'
import FarmerCharacterReference from 'App/Models/FarmerCharacterReference'
import FarmerChemicalPivot from 'App/Models/FarmerChemicalPivot'
import FarmerCreditScore from 'App/Models/FarmerCreditScore'
import FarmerCreditScoreHistory from 'App/Models/FarmerCreditScoreHistory'
import FarmerCropPivot from 'App/Models/FarmerCropPivot'
import FarmerFamilyProfile from 'App/Models/FarmerFamilyProfile'
import FarmerFertilizerPivot from 'App/Models/FarmerFertilizerPivot'
import FarmerGovernmentIdentification from 'App/Models/FarmerGovernmentIdentification'
import FarmerInfo from 'App/Models/FarmerInfo'
import FarmerInsurance from 'App/Models/FarmerInsurance'
import FarmerMobileDevice from 'App/Models/FarmerMobileDevice'
import FarmerRealProperty from 'App/Models/FarmerRealProperty'
import FarmerReferrer from 'App/Models/FarmerReferrer'
import FarmerSeedPivot from 'App/Models/FarmerSeedPivot'
import FarmerSeedSubcategoryPivot from 'App/Models/FarmerSeedSubcategoryPivot'
import FarmerSubCropPivot from 'App/Models/FarmerSubCropPivot'
import FarmerUtility from 'App/Models/FarmerUtility'
import FarmerVehicle from 'App/Models/FarmerVehicle'
import FarmerVouchLeader from 'App/Models/FarmerVouchLeader'
import FarmerVouchMao from 'App/Models/FarmerVouchMao'
import User, { UserStatusType, UserType } from 'App/Models/User'
import Wallet from 'App/Models/Wallet'
import FarmerRegistrationValidator from 'App/Validators/FarmerRegistrationValidator'
import { DateTime } from 'luxon'

export default class AuthController {
  public async registerFarmer({ request, response }: HttpContextContract) {
    const {
      email,
      password,
      firstName,
      lastName,
      middleName,
      userImage,
      birthDate,
      placeOfBirth,
      religion,
      gender,
      civilStatus,
      height,
      weight,
      mobileNumber,
      addressHouseNumber,
      addressStreet,
      addressProvince,
      addressCity,
      addressBarangay,
      addressZipCode,
      educationalAttainment,
      educationalIsGraduate,
      educationalDegree,
      occupationTitle,
      occupation,
      occupationStatus,
      occupationEmployerName,
      occupationEmployerAddress,
      occupationBusinessName,
      occupationBusinessAddress,
      occupationBusinessContact,
      occupationAnnualIncome,
      skillsFarming,
      skillsFishing,
      skillsLivestock,
      skillsConstruction,
      skillsProcessing,
      skillsServicing,
      skillsCraft,
      skillsOthers,
      governmentIdentification,
      familyProfile,
      realProperty,
      bankDetail,
      characterReference,
      vehicleOwned,
      farmAddress,
      farmArea,
      farmOwnership,
      farmerVehicle,
      cropsPlanted,
      subCropsPlanted,
      seed,
      seedSubcategory,
      fertilizer,
      chemical,
      landCategory,
      crop,
      phase,
      ownerCultivator,
      tenant,
      cltEp,
      lessee,
      naturalDisasterCover,
      multiRiskCover,
      desiredAmountCover,
      additionalAmountCover,
      transplantingDate,
      harvestDate,
      sowingDate,
      seedbedding,
      planting,
      plantCare,
      insurancePremium,
      insuranceLocationPlan,
      nationality,
      yearResiding,
      residenceOwnership,
      otherMobileNumber,
      vouchByLeader,
      vouchByMao,
      referrer,
      biometric,
      mobileDeviceBrand,
      mobileDeviceModel,
      farmerUtilities,
      permanentAddressHouseNumber,
      permanentAddressStreet,
      permanentAddressProvince,
      permanentAddressCity,
      permanentAddressBarangay,
      permanentAddressZipCode,
      permanentAddressLengthOfStay,
      addressLengthOfStay,
      sourceOfFunds,
      landbankAccounts,
      priceBasedBy,
      facialRecognition,
      purchaserSellingLocation,
      purchaserFullname,
      purchaserContactNumber,
      farmAddressHouseNumber,
      farmAddressStreet,
      farmAddressProvince,
      farmAddressCity,
      farmAddressBarangay,
      farmAddressZipCode,
      farmAddressCountry,
      telephoneNumber,
      mothersMaidenName,
      facebookName,
    } = await request.validate(FarmerRegistrationValidator)

    if (userImage) {
      await userImage.moveToDisk('./users/profile')
    }

    if (biometric) {
      await biometric.moveToDisk('./users/biometric')
    }

    if (facialRecognition) {
      await facialRecognition.moveToDisk('./users/facial_recognition')
    }

    if (insuranceLocationPlan) {
      await insuranceLocationPlan.moveToDisk('./users/insurance')
    }

    const isExistingEmailUser = await User.findBy(
      'email',
      email ??
        `${firstName}.${lastName}.${DateTime.local().toSQLDate()}`
          .replace(/\s/g, '')
          .replace(/-/g, '')
          .toLowerCase()
    )

    if (isExistingEmailUser) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User already registered',
      })
    }

    const isExistingUsername = await User.findBy(
      'username',
      `${firstName}.${lastName}.${DateTime.local().toSQLDate()}`
        .replace(/\s/g, '')
        .replace(/-/g, '')
        .toLowerCase()
    )

    if (isExistingUsername) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User already registered',
      })
    }

    const createUser = await User.create({
      email:
        email ??
        `${firstName}.${lastName}.${DateTime.local().toSQLDate()}`
          .replace(/\s/g, '')
          .replace(/-/g, '')
          .toLowerCase(),
      password: password ?? 'password',
      userImg: userImage?.fileName,
      userType: UserType.FARMER,
      username: `${firstName}.${lastName}.${DateTime.local().toSQLDate()}`
        .replace(/\s/g, '')
        .replace(/-/g, '')
        .toLowerCase(),
      status: UserStatusType.PENDING_FROM_ADMIN,
    })

    await Wallet.create({
      userId: createUser.id,
      credit: 0,
      balance: 0,
      payment: 0,
    })

    const qrCode = (
      new Date().getTime().toString(36) + Math.random().toString(36).slice(2)
    ).toUpperCase()

    const createFarmer = await Farmer.create({
      userId: createUser.id,
      mobileNumber,
      firstName,
      lastName,
      middleName,
      birthDate,
      placeOfBirth,
      religion,
      gender,
      civilStatus,
      height,
      weight,
      address: JSON.stringify({
        addressHouseNumber,
        addressStreet,
        addressProvince,
        addressCity,
        addressBarangay,
        addressZipCode,
      }),
      addressHouseNumber,
      addressStreet,
      addressProvince,
      addressCity,
      addressBarangay,
      addressZipCode,
      educationalAttainment,
      educationalIsGraduate,
      educationalDegree,
      occupationTitle,
      occupation,
      occupationStatus,
      occupationEmployerName,
      occupationEmployerAddress,
      occupationBusinessName,
      occupationBusinessAddress,
      occupationBusinessContact,
      occupationAnnualIncome,
      skillsFarming,
      skillsFishing,
      skillsLivestock,
      skillsConstruction,
      skillsProcessing,
      skillsServicing,
      skillsCraft,
      skillsOthers,
      vehicleOwned,
      biometric: biometric?.fileName,
      hasBiometric: biometric ? 1 : 0,
      facialRecognition: facialRecognition?.fileName,
      hasFacialRecognition: facialRecognition ? 1 : 0,
      qrCode,
      permanentAddress: JSON.stringify({
        permanentAddressHouseNumber,
        permanentAddressStreet,
        permanentAddressProvince,
        permanentAddressCity,
        permanentAddressBarangay,
        permanentAddressZipCode,
      }),
      permanentAddressHouseNumber,
      permanentAddressStreet,
      permanentAddressProvince,
      permanentAddressCity,
      permanentAddressBarangay,
      permanentAddressZipCode,
      permanentAddressLengthOfStay,
      addressLengthOfStay,
      sourceOfFunds,
      landbankAccounts,
      telephoneNumber,
      mothersMaidenName,
      facebookName,
    })

    await FarmerInfo.create({
      farmerId: createFarmer.id,
      farmAddress,
      farmAddressHouseNumber,
      farmAddressStreet,
      farmAddressProvince,
      farmAddressCity,
      farmAddressBarangay,
      farmAddressZipCode,
      farmAddressCountry,
      farmOwnership,
      farmArea,
      nationality,
      yearResiding,
      residenceOwnership,
      otherMobileNumber,
      priceBasedBy,
      purchaserSellingLocation,
      purchaserFullname,
      purchaserContactNumber,
    })

    await FarmerInsurance.create({
      farmerId: createFarmer.id,
      landCategory,
      crop,
      phase,
      ownerCultivator,
      tenant,
      cltEp,
      lessee,
      naturalDisasterCover,
      multiRiskCover,
      desiredAmountCover,
      additionalAmountCover,
      totalAmountCover:
        (desiredAmountCover &&
          additionalAmountCover &&
          desiredAmountCover + additionalAmountCover) ||
        0,
      transplantingDate,
      harvestDate,
      sowingDate,
      seedbedding: {
        data: seedbedding,
      },
      planting: {
        data: planting,
      },
      plantCare: {
        data: plantCare,
      },
      insurancePremium,
      insuranceLocationPlan: insuranceLocationPlan?.fileName,
    })

    if (cropsPlanted && cropsPlanted.length > 0) {
      const mappedCropsPlanted = cropsPlanted?.map((cropId) => {
        return {
          farmerId: createFarmer.id,
          cropId: cropId,
        }
      })

      await FarmerCropPivot.createMany(mappedCropsPlanted)
    }

    if (subCropsPlanted && subCropsPlanted.length > 0) {
      const mappedSubCropsPlanted = subCropsPlanted?.map((cropId) => {
        return {
          farmerId: createFarmer.id,
          cropId: cropId,
        }
      })

      await FarmerSubCropPivot.createMany(mappedSubCropsPlanted)
    }

    if (fertilizer && fertilizer.length > 0) {
      const mappedFertilizer = fertilizer?.map((fertilizerId) => {
        return {
          farmerId: createFarmer.id,
          fertilizerId: fertilizerId,
        }
      })

      await FarmerFertilizerPivot.createMany(mappedFertilizer)
    }

    if (seed && seed.length > 0) {
      const mappedSeed = seed?.map((seedId) => {
        return {
          farmerId: createFarmer.id,
          seedId: seedId,
        }
      })

      await FarmerSeedPivot.createMany(mappedSeed)
    }

    if (seedSubcategory && seedSubcategory.length > 0) {
      const mappedSeedSubcategory = seedSubcategory?.map((seedSubcategoryId) => {
        return {
          farmerId: createFarmer.id,
          seedSubcategoryId: seedSubcategoryId,
        }
      })

      await FarmerSeedSubcategoryPivot.createMany(mappedSeedSubcategory)
    }

    if (chemical && chemical.length > 0) {
      const mappedChemical = chemical?.map((chemicalId) => {
        return {
          farmerId: createFarmer.id,
          chemicalId: chemicalId,
        }
      })

      await FarmerChemicalPivot.createMany(mappedChemical)
    }

    if (familyProfile && familyProfile.length > 0) {
      const mappedFamilyProfile = familyProfile?.map((familyProfileItem) => {
        return {
          ...familyProfileItem,
          farmerId: createFarmer.id,
          isBarbazaMember: familyProfileItem.isBarbazaMember,
          isBeneficiaries: familyProfileItem.isBeneficiaries,
          identifier: `${createFarmer.id}-${familyProfileItem.name}`,
        }
      })

      await FarmerFamilyProfile.createMany(mappedFamilyProfile)
    }

    if (realProperty && realProperty.length > 0) {
      const mappedRealProperty = realProperty?.map((realPropertyItem) => {
        return {
          ...realPropertyItem,
          farmerId: createFarmer.id,
          identifier: `${createFarmer.id}-${realPropertyItem.propertyTitleNumber}`,
        }
      })

      await FarmerRealProperty.createMany(mappedRealProperty)
    }

    if (bankDetail && bankDetail.length > 0) {
      const mappedBankingDetails = bankDetail?.map((bankDetailItem) => {
        return {
          ...bankDetailItem,
          farmerId: createFarmer.id,
          identifier: `${createFarmer.id}-${bankDetailItem.bankAccountNumber}`,
        }
      })

      await FarmerBankingDetail.createMany(mappedBankingDetails)
    }

    if (characterReference && characterReference.length > 0) {
      const mappedCharacterReferences = characterReference?.map((characterReferenceItem) => {
        return {
          ...characterReferenceItem,
          farmerId: createFarmer.id,
          identifier: `${createFarmer.id}-${characterReferenceItem.name}`,
        }
      })

      await FarmerCharacterReference.createMany(mappedCharacterReferences)
    }

    if (farmerVehicle && farmerVehicle.length > 0) {
      const mappedFarmerVehicle = await Promise.all(
        farmerVehicle?.map(async (farmerVehicleItem) => {
          if (farmerVehicleItem && farmerVehicleItem.vehiclePlateNumber) {
            const entryAttachment = request.file(
              `farmerVehicle_vehicleOrcr_${farmerVehicleItem.vehiclePlateNumber}`,
              {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              }
            )

            await entryAttachment?.moveToDisk('./users/vehicleorcr')

            return {
              ...farmerVehicleItem,
              farmerId: createFarmer.id,
              vehicleOrcr: entryAttachment?.fileName ?? undefined,
              identifier: `${createFarmer.id}-${farmerVehicleItem.vehiclePlateNumber}`,
            }
          }

          return {
            ...farmerVehicleItem,
            farmerId: createFarmer.id,
            identifier: `${createFarmer.id}-${farmerVehicleItem.vehiclePlateNumber}`,
          }
        })
      )

      await FarmerVehicle.createMany(mappedFarmerVehicle)
    }

    if (governmentIdentification && governmentIdentification.length > 0) {
      const mappedGovernmentIdentification = await Promise.all(
        governmentIdentification?.map(async (governmentIdentificationItem) => {
          if (governmentIdentificationItem && governmentIdentificationItem.governmentIdNumber) {
            const entryAttachment = request.file(
              `governmentIdentification_${governmentIdentificationItem.governmentIdNumber}`,
              {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              }
            )

            if (entryAttachment) {
              await entryAttachment?.moveToDisk('./users/governmentid')

              return {
                ...governmentIdentificationItem,
                farmerId: createFarmer.id,
                governmentIdImage: entryAttachment?.fileName ?? undefined,
                identifier: `${createFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
              }
            }

            return {
              ...governmentIdentificationItem,
              farmerId: createFarmer.id,
              identifier: `${createFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
            }
          }

          return {
            ...governmentIdentificationItem,
            farmerId: createFarmer.id,
            identifier: `${createFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
          }
        })
      )

      await FarmerGovernmentIdentification.createMany(mappedGovernmentIdentification)
    }

    if (vouchByLeader && vouchByLeader.length > 0) {
      const mappedVouchByLeader = await Promise.all(
        vouchByLeader?.map(async (vouchByLeaderItem) => {
          if (vouchByLeaderItem && vouchByLeaderItem.identifier) {
            const entryAttachment = request.file(`vouchByLeader_${vouchByLeaderItem.identifier}`, {
              size: '10mb',
              extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
            })

            await entryAttachment?.moveToDisk('./users/vouchbyleader')

            return {
              ...vouchByLeaderItem,
              farmerId: createFarmer.id,
              vouchByLeadersAttachment: entryAttachment?.fileName ?? undefined,
            }
          }

          return {
            ...vouchByLeaderItem,
            farmerId: createFarmer.id,
          }
        })
      )

      await FarmerVouchLeader.createMany(mappedVouchByLeader)
    }

    if (vouchByMao && vouchByMao.length > 0) {
      const mappedVouchByMao = await Promise.all(
        vouchByMao?.map(async (vouchByMaoItem) => {
          if (vouchByMaoItem && vouchByMaoItem.identifier) {
            const entryAttachment = request.file(`vouchByMao_${vouchByMaoItem.identifier}`, {
              size: '10mb',
              extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
            })

            await entryAttachment?.moveToDisk('./users/vouchbymao')

            return {
              ...vouchByMaoItem,
              farmerId: createFarmer.id,
              vouchByMaosAttachment: entryAttachment?.fileName ?? undefined,
            }
          }

          return {
            ...vouchByMaoItem,
            farmerId: createFarmer.id,
          }
        })
      )

      await FarmerVouchMao.createMany(mappedVouchByMao)
    }

    if (referrer && referrer.length > 0) {
      const mappedReferrer = await Promise.all(
        referrer?.map(async (referrerItem) => {
          if (referrerItem && referrerItem.identifier) {
            const entryAttachment = request.file(`referrer_${referrerItem.identifier}`, {
              size: '10mb',
              extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
            })

            await entryAttachment?.moveToDisk('./users/referrer')

            return {
              ...referrerItem,
              farmerId: createFarmer.id,
              referrerAttachment: entryAttachment?.fileName ?? undefined,
            }
          }

          return {
            ...referrerItem,
            farmerId: createFarmer.id,
          }
        })
      )

      await FarmerReferrer.createMany(mappedReferrer)
    }

    if (mobileDeviceBrand || mobileDeviceModel) {
      await FarmerMobileDevice.create({
        farmerId: createFarmer.id,
        mobileDeviceBrand,
        mobileDeviceModel,
      })
    }

    if (farmerUtilities && farmerUtilities.length > 0) {
      const mappedFarmerUtilities = await Promise.all(
        farmerUtilities?.map(async (farmerUtilitiesItem) => {
          if (farmerUtilitiesItem && farmerUtilitiesItem.identifier) {
            const entryAttachment = request.file(
              `farmerUtilities_${farmerUtilitiesItem.identifier}`,
              {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              }
            )

            await entryAttachment?.moveToDisk('./users/utility')

            return {
              ...farmerUtilitiesItem,
              farmerId: createFarmer.id,
              bill: entryAttachment?.fileName ?? undefined,
            }
          }

          return {
            ...farmerUtilitiesItem,
            farmerId: createFarmer.id,
          }
        })
      )

      await FarmerUtility.createMany(mappedFarmerUtilities)
    }

    const getAllFinancingCompany = await CreditScoreGroup.query().where(
      'status',
      CreditScoreGroupStatus.ACTIVE
    )

    const mappedValue = getAllFinancingCompany?.map((item) => ({
      farmerId: createFarmer.id,
      creditScoreGroupId: item.id,
      loanCycleNumber: 1,
    }))

    if (mappedValue && mappedValue.length > 0) {
      await FarmerCreditScore.createMany(mappedValue)
      await FarmerCreditScoreHistory.createMany(mappedValue)
    }

    await triggerCreditScoreComputation(createUser.id)

    await initializeFarmerLoanRequirement(createFarmer.id)

    return response.json({
      status: 1,
      message: 'Farmer registered successfully!',
    })
  }

  public async login({ auth, request, response }: HttpContextContract) {
    const { email, password } = request.body()

    if (!email) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Email is required!',
      })
    }

    if (!password) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Password is required!',
      })
    }

    const userStatus = await User.query().where('email', email).orWhere('username', email).first()

    if (!userStatus) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Email not found!',
      })
    }

    if (userStatus.status === UserStatusType.PENDING_FROM_ADMIN) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Account deactivated, please contact admin/manager!',
      })
    }

    if (userStatus.status === UserStatusType.BLOCKED) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Account blocked, please contact admin/manager!',
      })
    }

    try {
      const token = await auth.use('api').attempt(email, password)

      if (auth.user?.userType === UserType.FARMER) {
        await auth.user?.load('farmer', (subQuery) => {
          subQuery.preload('farmerInfo').preload('cropsPlanted').preload('subCropsPlanted')
        })
      }

      if (
        auth.user?.userType === UserType.ENCODER_1 ||
        auth.user?.userType === UserType.ENCODER_2
      ) {
        await auth.user?.load('encoder')
      }

      if (auth.user?.userType === UserType.ADMIN || auth.user?.userType === UserType.SUPERADMIN) {
        await auth.user?.load('admin')
      }

      if (
        auth.user?.userType === UserType.FINANCE_1 ||
        auth.user?.userType === UserType.FINANCE_2
      ) {
        await auth.user?.load('finance')
      }

      if (auth.user?.userType === UserType.SALE_1 || auth.user?.userType === UserType.SALE_2) {
        await auth.user?.load('sale')
      }

      if (
        auth.user?.userType === UserType.OPERATION_1 ||
        auth.user?.userType === UserType.OPERATION_2
      ) {
        await auth.user?.load('operation')
      }

      if (
        auth.user?.userType === UserType.AGRONOMIST ||
        auth.user?.userType === UserType.HEAD_AGRONOMIST
      ) {
        await auth.user?.load('agronomist')
      }

      if (auth.user?.userType === UserType.FIELD_RELATION_OFFICER) {
        await auth.user?.load('fieldRelationOfficer')
      }

      await AuditLog.create({
        userId: auth?.user?.id,
        action: 'LOGIN',
      })

      return response.json({
        status: 1,
        message: 'Login success!',
        data: {
          user: auth.user,
          token: token.token,
          isSuperAdmin: userStatus.userType === UserType.SUPERADMIN,
          isAdmin: userStatus.userType === UserType.ADMIN,
          isFarmer: userStatus.userType === UserType.FARMER,
          isEncoder1: userStatus.userType === UserType.ENCODER_1,
          isEncoder2: userStatus.userType === UserType.ENCODER_2,
          isOperation1: userStatus.userType === UserType.OPERATION_1,
          isOperation2: userStatus.userType === UserType.OPERATION_2,
          isAgronomist: userStatus.userType === UserType.AGRONOMIST,
          isHeadAgronomist: userStatus.userType === UserType.HEAD_AGRONOMIST,
          isFieldRelationOfficer: userStatus.userType === UserType.FIELD_RELATION_OFFICER,
          isSale1: userStatus.userType === UserType.SALE_1,
          isSale2: userStatus.userType === UserType.SALE_2,
          isFinance1: userStatus.userType === UserType.FINANCE_1,
          isFinance2: userStatus.userType === UserType.FINANCE_2,
        },
      })
    } catch {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid Credentials',
      })
    }
  }

  public async logout({ auth, response }: HttpContextContract) {
    await auth.logout()

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'LOGOUT',
    })

    return response.json({
      status: 1,
      message: 'Successfully logged out user',
    })
  }
}
