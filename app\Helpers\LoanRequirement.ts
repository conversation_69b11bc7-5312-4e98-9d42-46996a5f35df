import Bull from '@ioc:Rocketseat/Bull'
import ProcessInitializeLoanRequirementJob from 'App/Jobs/ProcessInitializeLoanRequirementJob'
import ProcessLoanRequirementJob from 'App/Jobs/ProcessLoanRequirementJob'
import Farmer from 'App/Models/Farmer'
import { GovernmentIdentificationType } from 'App/Models/FarmerGovernmentIdentification'
import FarmerLoanRequirement, {
  EFarmerLoanRequirementItemStage,
  stageFourLoanRequirements,
  stageOneLoanRequirements,
  stageThreeLoanRequirements,
  stageTwoLoanRequirements,
  TFarmerLoanRequirementStage,
  TFarmerLoanRequirementStageFour,
  TFarmerLoanRequirementStageOne,
  TFarmerLoanRequirementStageThree,
  TFarmerLoanRequirementStageTwo,
} from 'App/Models/FarmerLoanRequirement'
import FarmerLoanRequirementItem, {
  EFarmerLoanRequirementItemStatus,
} from 'App/Models/FarmerLoanRequirementItem'
import LoanApplicationSubmission, {
  LoanApplicationSubmissionStatus,
} from 'App/Models/LoanApplicationSubmission'

export const triggerLoanRequirementComputation = async (
  farmerId: number,
  name: TFarmerLoanRequirementStage
) => {
  await Bull.add(
    new ProcessLoanRequirementJob().key,
    {
      farmer_id: farmerId,
      name: name,
    },
    {
      attempts: 5,
    }
  )
}

export const initializeFarmerLoanRequirement = async (farmerId: number) => {
  await Bull.add(
    new ProcessInitializeLoanRequirementJob().key,
    {
      farmer_id: farmerId,
    },
    {
      attempts: 5,
    }
  )
}

export const recomputeLoanRequirement = async (
  farmerId: number,
  name: TFarmerLoanRequirementStage
): Promise<any> => {
  const loanRequirementItem = await FarmerLoanRequirementItem.query()
    .preload('farmerLoanRequirement')
    .preload('farmer', (subQuery) => {
      subQuery.preload('user').preload('governmentIdentifications')
    })
    .where('farmerId', farmerId)
    .where('name', name)
    .first()

  if (!loanRequirementItem) {
    return null
  }

  const loanRequirementSummary = loanRequirementItem.farmerLoanRequirement.stageSummary || {
    ...(loanRequirementItem.farmerLoanRequirement.stage ===
      EFarmerLoanRequirementItemStage.STAGE_ONE && {
      '2x2': loanRequirementItem.farmer?.user?.userImg ? true : false,
      'bir_tin': loanRequirementItem.farmer?.governmentIdentifications?.find(
        (item) => item.governmentIdType === GovernmentIdentificationType.TIN
      )
        ? true
        : false,
      'rsbsa_id': loanRequirementItem.farmer?.governmentIdentifications?.find(
        (item) => item.governmentIdType === GovernmentIdentificationType.RSBSA
      )
        ? true
        : false,
      'government_id':
        loanRequirementItem.farmer?.governmentIdentifications?.length > 0 ? true : false,
      'barangay_clearance': false,
      'land_agreement': false,
      'kita_loan_form': false,
      'lbp_agrisenso_loan_form': false,
      'lbp_data_privacy': false,
      'farmer_meeting': false,
    }),
    ...(loanRequirementItem.farmerLoanRequirement.stage ===
      EFarmerLoanRequirementItemStage.STAGE_TWO && {
      nia_certification: false,
      ia_certification: false,
      dar_certification: false,
      mao_certification: false,
      pcic: false,
      ptma: false,
      msa: false,
      farm_plan: false,
      tripartite_agreement: false,
      signed_notarized_documents: false,
    }),
    ...(loanRequirementItem.farmerLoanRequirement.stage ===
      EFarmerLoanRequirementItemStage.STAGE_THREE && {
      submission_of_documents: false,
      verification_of_documents: false,
      promissory_note_signing: false,
      opening_of_account: false,
    }),
    ...(loanRequirementItem.farmerLoanRequirement.stage ===
      EFarmerLoanRequirementItemStage.STAGE_FOUR && {
      agri_inputs_distribution: false,
      submission_of_soa: false,
      transfer_of_cash: false,
      amount_to_be_credited: false,
    }),
  }

  loanRequirementSummary[name] =
    loanRequirementItem.isCompleted === EFarmerLoanRequirementItemStatus.COMPLETED

  //   update stageSummary
  loanRequirementItem.farmerLoanRequirement.stageSummary = loanRequirementSummary

  //   update stageCount
  loanRequirementItem.farmerLoanRequirement.stageCount = Object.values(
    loanRequirementSummary
  ).filter((value) => value).length

  //   update stageStartedAt
  if (loanRequirementItem.farmerLoanRequirement.stageStartedAt === null) {
    loanRequirementItem.farmerLoanRequirement.stageStartedAt = loanRequirementItem.createdAt
  }

  //   update stageCompletedAt
  if (
    loanRequirementItem.farmerLoanRequirement.stage === EFarmerLoanRequirementItemStage.STAGE_ONE
  ) {
    loanRequirementItem.farmerLoanRequirement.stageCompletedAt =
      loanRequirementItem.farmerLoanRequirement.stageCount === stageOneLoanRequirements.length
        ? loanRequirementItem.updatedAt
        : null
  }

  if (
    loanRequirementItem.farmerLoanRequirement.stage === EFarmerLoanRequirementItemStage.STAGE_TWO
  ) {
    loanRequirementItem.farmerLoanRequirement.stageCompletedAt =
      loanRequirementItem.farmerLoanRequirement.stageCount === stageTwoLoanRequirements.length
        ? loanRequirementItem.updatedAt
        : null
  }

  if (
    loanRequirementItem.farmerLoanRequirement.stage === EFarmerLoanRequirementItemStage.STAGE_THREE
  ) {
    loanRequirementItem.farmerLoanRequirement.stageCompletedAt =
      loanRequirementItem.farmerLoanRequirement.stageCount === stageThreeLoanRequirements.length
        ? loanRequirementItem.updatedAt
        : null
  }

  if (
    loanRequirementItem.farmerLoanRequirement.stage === EFarmerLoanRequirementItemStage.STAGE_FOUR
  ) {
    loanRequirementItem.farmerLoanRequirement.stageCompletedAt =
      loanRequirementItem.farmerLoanRequirement.stageCount === stageFourLoanRequirements.length
        ? loanRequirementItem.updatedAt
        : null
  }

  await loanRequirementItem.farmerLoanRequirement.save()

  const findLoanSubmission = await LoanApplicationSubmission.query()
    .where('userId', loanRequirementItem.farmer.userId)
    .where('status', LoanApplicationSubmissionStatus.PENDING)
    .first()

  if (findLoanSubmission) {
    const findPendingStage = await FarmerLoanRequirement.query()
      .where('farmerId', farmerId)
      .whereNull('stageCompletedAt')
      .orderBy('stage', 'asc')
      .first()

    findLoanSubmission.loanApplicationStage = findPendingStage?.stage || 1
    findLoanSubmission.loanApplicationCompletionCount = findPendingStage?.stageCount || 0

    await findLoanSubmission.save()
  }

  return loanRequirementItem.toJSON()
}

export const initializeLoanRequirement = async (farmerId: number) => {
  const findFarmer = await Farmer.query()
    .preload('user')
    .preload('governmentIdentifications')
    .where('id', farmerId)
    .first()

  if (!findFarmer) {
    return
  }

  const totalStage = EFarmerLoanRequirementItemStage.STAGE_FOUR

  for (let currentStage = 1; currentStage <= totalStage; currentStage++) {
    const loanRequirementSummary: Record<string, boolean> = {
      ...(currentStage === EFarmerLoanRequirementItemStage.STAGE_ONE &&
        ({
          '2x2': findFarmer?.user?.userImg ? true : false,
          'bir_tin': findFarmer?.governmentIdentifications?.find(
            (item) => item.governmentIdType === GovernmentIdentificationType.TIN
          )
            ? true
            : false,
          'rsbsa_id': findFarmer?.governmentIdentifications?.find(
            (item) => item.governmentIdType === GovernmentIdentificationType.RSBSA
          )
            ? true
            : false,
          'government_id': findFarmer?.governmentIdentifications?.length > 0 ? true : false,
          'barangay_clearance': false,
          'land_agreement': false,
          'kita_loan_form': false,
          'lbp_agrisenso_loan_form': false,
          'lbp_data_privacy': false,
          'farmer_meeting': false,
        } as Record<TFarmerLoanRequirementStageOne, boolean>)),
      ...(currentStage === EFarmerLoanRequirementItemStage.STAGE_TWO &&
        ({
          nia_certification: false,
          ia_certification: false,
          dar_certification: false,
          mao_certification: false,
          pcic: false,
          ptma: false,
          msa: false,
          farm_plan: false,
          tripartite_agreement: false,
          signed_notarized_documents: false,
        } as Record<TFarmerLoanRequirementStageTwo, boolean>)),
      ...(currentStage === EFarmerLoanRequirementItemStage.STAGE_THREE &&
        ({
          submission_of_documents: false,
          verification_of_documents: false,
          promissory_note_signing: false,
          opening_of_account: false,
        } as Record<TFarmerLoanRequirementStageThree, boolean>)),
      ...(currentStage === EFarmerLoanRequirementItemStage.STAGE_FOUR &&
        ({
          agri_inputs_distribution: false,
          submission_of_soa: false,
          transfer_of_cash: false,
          amount_to_be_credited: false,
        } as Record<TFarmerLoanRequirementStageFour, boolean>)),
    }

    const findFarmerLoanRequirement = await FarmerLoanRequirement.firstOrCreate(
      {
        farmerId,
        stage: currentStage,
      },
      {
        farmerId,
        stage: currentStage,
        stageSummary: loanRequirementSummary,
        stageCount: Object.values(loanRequirementSummary).filter((value) => value).length,
      }
    )

    if (currentStage === EFarmerLoanRequirementItemStage.STAGE_ONE) {
      stageOneLoanRequirements.forEach(async (name) => {
        await FarmerLoanRequirementItem.firstOrCreate(
          {
            farmerId,
            farmerLoanRequirementId: findFarmerLoanRequirement.id,
            name: name,
          },
          {
            farmerId,
            farmerLoanRequirementId: findFarmerLoanRequirement.id,
            name: name,
            stage: currentStage,
            isCompleted: loanRequirementSummary[name]
              ? EFarmerLoanRequirementItemStatus.COMPLETED
              : EFarmerLoanRequirementItemStatus.PENDING,
          }
        )
      })
    }

    if (currentStage === EFarmerLoanRequirementItemStage.STAGE_TWO) {
      stageTwoLoanRequirements.forEach(async (name) => {
        await FarmerLoanRequirementItem.firstOrCreate(
          {
            farmerId,
            farmerLoanRequirementId: findFarmerLoanRequirement.id,
            name: name,
          },
          {
            farmerId,
            farmerLoanRequirementId: findFarmerLoanRequirement.id,
            name: name,
            stage: currentStage,
            isCompleted: loanRequirementSummary[name]
              ? EFarmerLoanRequirementItemStatus.COMPLETED
              : EFarmerLoanRequirementItemStatus.PENDING,
          }
        )
      })
    }

    if (currentStage === EFarmerLoanRequirementItemStage.STAGE_THREE) {
      stageThreeLoanRequirements.forEach(async (name) => {
        await FarmerLoanRequirementItem.firstOrCreate(
          {
            farmerId,
            farmerLoanRequirementId: findFarmerLoanRequirement.id,
            name: name,
          },
          {
            farmerId,
            farmerLoanRequirementId: findFarmerLoanRequirement.id,
            name: name,
            stage: currentStage,
            isCompleted: loanRequirementSummary[name]
              ? EFarmerLoanRequirementItemStatus.COMPLETED
              : EFarmerLoanRequirementItemStatus.PENDING,
          }
        )
      })
    }

    if (currentStage === EFarmerLoanRequirementItemStage.STAGE_FOUR) {
      stageFourLoanRequirements.forEach(async (name) => {
        await FarmerLoanRequirementItem.firstOrCreate(
          {
            farmerId,
            farmerLoanRequirementId: findFarmerLoanRequirement.id,
            name: name,
          },
          {
            farmerId,
            farmerLoanRequirementId: findFarmerLoanRequirement.id,
            name: name,
            stage: currentStage,
            isCompleted: loanRequirementSummary[name]
              ? EFarmerLoanRequirementItemStatus.COMPLETED
              : EFarmerLoanRequirementItemStatus.PENDING,
          }
        )
      })
    }
  }

  const findLoanSubmission = await LoanApplicationSubmission.query()
    .where('userId', findFarmer.userId)
    .where('status', LoanApplicationSubmissionStatus.PENDING)
    .first()

  if (findLoanSubmission) {
    const findPendingStage = await FarmerLoanRequirement.query()
      .where('farmerId', farmerId)
      .whereNull('stageCompletedAt')
      .orderBy('stage', 'asc')
      .first()

    findLoanSubmission.loanApplicationStage = findPendingStage?.stage || 1
    findLoanSubmission.loanApplicationCompletionCount = findPendingStage?.stageCount || 0

    await findLoanSubmission.save()
  }
}
