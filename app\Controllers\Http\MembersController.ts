import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Farmer, { <PERSON><PERSON><PERSON>, HasSubmittedLoanApplication } from 'App/Models/Farmer'
import FarmerCreditScore from 'App/Models/FarmerCreditScore'
import { EFarmerLoanRequirementItemStatus } from 'App/Models/FarmerLoanRequirementItem'
import LoanApplicationSubmission, {
  LoanApplicationSubmissionStatus,
} from 'App/Models/LoanApplicationSubmission'
import LoanApplicationSubmissionCreditScoreGroupPivot from 'App/Models/LoanApplicationSubmissionCreditScoreGroupPivot'
import LoanPeriodTracker, { LoanPeriodTrackerStatus } from 'App/Models/LoanPeriodTracker'
import User, { UserType } from 'App/Models/User'
import { DateTime } from 'luxon'

export enum RatingType {
  POOR = 0,
  FAIR = 1,
  GOOD = 2,
  VERY_GOOD = 3,
  EXCELLENT = 4,
}

export default class MembersController {
  public async nonLoanHolderDashboard({ response, request }: HttpContextContract) {
    const {
      creditScoreGroupId = 1,
      lastTransactionDays,
      totalTradingpostTransactionStart,
      totalTradingpostTransactionEnd,
      totalMarketplaceTransactionStart,
      totalMarketplaceTransactionEnd,
      totalSalesTransactionStart,
      totalSalesTransactionEnd,
      startDate,
      endDate,
      ratings = [],
    } = request.qs()

    let allNonLoan = 0
    let excellentNonLoan = 0
    let veryGoodNonLoan = 0
    let goodNonLoan = 0
    let fairNonLoan = 0
    let poorNonLoan = 0

    let getAllNonLoanHolderUser = null as any

    getAllNonLoanHolderUser = FarmerCreditScore.query()
      .where('creditScoreGroupId', creditScoreGroupId)
      .whereHas('farmer', (subQuery) => {
        subQuery.where('hasLoan', HasLoan.NO)
      })

    if (startDate && endDate) {
      const start = DateTime.fromISO(startDate, { zone: 'Asia/Manila' }).startOf('day').toUTC()
      const end = DateTime.fromISO(endDate, { zone: 'Asia/Manila' }).endOf('day').toUTC()

      getAllNonLoanHolderUser = getAllNonLoanHolderUser.whereBetween('updatedAt', [
        start.toSQL(),
        end.toSQL(),
      ])
    }

    if (ratings && Array.isArray(ratings)) {
      getAllNonLoanHolderUser = getAllNonLoanHolderUser.where((subQuery) => {
        for (const rates of ratings) {
          switch (Number(rates)) {
            case RatingType.POOR:
              subQuery.orWhereBetween('beforeStageCreditScore', [0, 15])
              break
            case RatingType.FAIR:
              subQuery.orWhereBetween('beforeStageCreditScore', [16, 59])
              break
            case RatingType.GOOD:
              subQuery.orWhereBetween('beforeStageCreditScore', [60, 70])
              break
            case RatingType.VERY_GOOD:
              subQuery.orWhereBetween('beforeStageCreditScore', [71, 85])
              break
            case RatingType.EXCELLENT:
              subQuery.orWhereBetween('beforeStageCreditScore', [86, 100])
              break

            default:
              break
          }
        }
      })
    }

    if (lastTransactionDays) {
      getAllNonLoanHolderUser = getAllNonLoanHolderUser.whereHas('farmer', (subQuery) => {
        subQuery.whereHas('farmerTransaction', (subQuery) => {
          subQuery
            .where(
              'tradingPostUpdatedAt',
              '>=',
              DateTime.local()
                .minus({ days: Number(lastTransactionDays) })
                .toSQLDate()!
            )
            .orWhere(
              'marketplaceUpdatedAt',
              '>=',
              DateTime.local()
                .minus({ days: Number(lastTransactionDays) })
                .toSQLDate()!
            )
            .orWhere(
              'salesUpdatedAt',
              '>=',
              DateTime.local()
                .minus({ days: Number(lastTransactionDays) })
                .toSQLDate()!
            )
        })
      })
    }

    if (
      (totalTradingpostTransactionStart || Number(totalTradingpostTransactionStart ?? 0) === 0) &&
      totalTradingpostTransactionEnd &&
      (totalMarketplaceTransactionStart || Number(totalMarketplaceTransactionStart ?? 0) === 0) &&
      totalMarketplaceTransactionEnd &&
      (totalSalesTransactionStart || Number(totalSalesTransactionStart ?? 0) === 0) &&
      totalSalesTransactionEnd
    ) {
      getAllNonLoanHolderUser = getAllNonLoanHolderUser.whereHas('farmer', (subQuery) => {
        subQuery.whereHas('farmerTransaction', (subQuery) => {
          subQuery
            .whereBetween('tradingPostBeforeLoanTotal', [
              Number(totalTradingpostTransactionStart),
              Number(totalTradingpostTransactionEnd),
            ])
            .whereBetween('marketplaceBeforeLoanTotal', [
              Number(totalMarketplaceTransactionStart),
              Number(totalMarketplaceTransactionEnd),
            ])
            .whereBetween('salesBeforeLoanTotal', [
              Number(totalSalesTransactionStart),
              Number(totalSalesTransactionEnd),
            ])
        })
      })
    }

    getAllNonLoanHolderUser = await getAllNonLoanHolderUser.orderBy(
      'beforeStageCreditScore',
      'desc'
    )

    for (const nonLoanHolderUser of getAllNonLoanHolderUser) {
      allNonLoan++

      if (nonLoanHolderUser.beforeStageCreditScore >= 86) {
        excellentNonLoan++
      } else if (nonLoanHolderUser.beforeStageCreditScore >= 71) {
        veryGoodNonLoan++
      } else if (nonLoanHolderUser.beforeStageCreditScore >= 60) {
        goodNonLoan++
      } else if (nonLoanHolderUser.beforeStageCreditScore >= 16) {
        fairNonLoan++
      } else {
        poorNonLoan++
      }
    }

    return response.json({
      status: 1,
      data: {
        allNonLoan,
        excellentNonLoan,
        veryGoodNonLoan,
        goodNonLoan,
        fairNonLoan,
        poorNonLoan,
      },
    })
  }

  public async nonLoanHolderViewAll({ request, response }: HttpContextContract) {
    const {
      startDate,
      endDate,
      ratingStart,
      ratingEnd,
      ratings = [],
      creditScoreGroupId,
      lastTransactionDays,
      totalTradingpostTransactionStart,
      totalTradingpostTransactionEnd,
      totalMarketplaceTransactionStart,
      totalMarketplaceTransactionEnd,
      totalSalesTransactionStart,
      totalSalesTransactionEnd,
      page = 1,
      pageSize = 10,
    } = request.qs()

    let getAllNonLoanHolderUser = null as any

    getAllNonLoanHolderUser = FarmerCreditScore.query()
      .preload('farmer', (subQuery) => {
        subQuery
          .select('id', 'user_id', 'first_name', 'last_name', 'middle_name')
          .preload('farmerTransaction')
      })
      .where('creditScoreGroupId', creditScoreGroupId)
      .whereHas('farmer', (subQuery) => {
        subQuery.where('hasLoan', HasLoan.NO)
      })

    if (ratingStart && ratingEnd) {
      getAllNonLoanHolderUser = getAllNonLoanHolderUser.whereBetween('beforeStageCreditScore', [
        Number(ratingStart),
        Number(ratingEnd),
      ])
    }

    if (startDate && endDate) {
      const start = DateTime.fromISO(startDate, { zone: 'Asia/Manila' }).startOf('day').toUTC()
      const end = DateTime.fromISO(endDate, { zone: 'Asia/Manila' }).endOf('day').toUTC()

      getAllNonLoanHolderUser = getAllNonLoanHolderUser.whereBetween('updatedAt', [
        start.toSQL(),
        end.toSQL(),
      ])
    }

    if (ratings && Array.isArray(ratings)) {
      getAllNonLoanHolderUser = getAllNonLoanHolderUser.where((subQuery) => {
        for (const rates of ratings) {
          switch (Number(rates)) {
            case RatingType.POOR:
              subQuery.orWhereBetween('beforeStageCreditScore', [0, 15])
              break
            case RatingType.FAIR:
              subQuery.orWhereBetween('beforeStageCreditScore', [16, 59])
              break
            case RatingType.GOOD:
              subQuery.orWhereBetween('beforeStageCreditScore', [60, 70])
              break
            case RatingType.VERY_GOOD:
              subQuery.orWhereBetween('beforeStageCreditScore', [71, 85])
              break
            case RatingType.EXCELLENT:
              subQuery.orWhereBetween('beforeStageCreditScore', [86, 100])
              break

            default:
              break
          }
        }
      })
    }

    if (lastTransactionDays) {
      getAllNonLoanHolderUser = getAllNonLoanHolderUser.whereHas('farmer', (subQuery) => {
        subQuery.whereHas('farmerTransaction', (subQuery) => {
          subQuery
            .where(
              'tradingPostUpdatedAt',
              '>=',
              DateTime.local()
                .minus({ days: Number(lastTransactionDays) })
                .toSQLDate()!
            )
            .orWhere(
              'marketplaceUpdatedAt',
              '>=',
              DateTime.local()
                .minus({ days: Number(lastTransactionDays) })
                .toSQLDate()!
            )
            .orWhere(
              'salesUpdatedAt',
              '>=',
              DateTime.local()
                .minus({ days: Number(lastTransactionDays) })
                .toSQLDate()!
            )
        })
      })
    }

    if (
      (totalTradingpostTransactionStart || Number(totalTradingpostTransactionStart ?? 0) === 0) &&
      totalTradingpostTransactionEnd &&
      (totalMarketplaceTransactionStart || Number(totalMarketplaceTransactionStart ?? 0) === 0) &&
      totalMarketplaceTransactionEnd &&
      (totalSalesTransactionStart || Number(totalSalesTransactionStart ?? 0) === 0) &&
      totalSalesTransactionEnd
    ) {
      getAllNonLoanHolderUser = getAllNonLoanHolderUser.whereHas('farmer', (subQuery) => {
        subQuery.whereHas('farmerTransaction', (subQuery) => {
          subQuery
            .whereBetween('tradingPostBeforeLoanTotal', [
              Number(totalTradingpostTransactionStart),
              Number(totalTradingpostTransactionEnd),
            ])
            .whereBetween('marketplaceBeforeLoanTotal', [
              Number(totalMarketplaceTransactionStart),
              Number(totalMarketplaceTransactionEnd),
            ])
            .whereBetween('salesBeforeLoanTotal', [
              Number(totalSalesTransactionStart),
              Number(totalSalesTransactionEnd),
            ])
        })
      })
    }

    getAllNonLoanHolderUser = await getAllNonLoanHolderUser
      .orderBy('beforeStageCreditScore', 'desc')
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: getAllNonLoanHolderUser,
    })
  }

  public async loanHolderDashboard({ response, request }: HttpContextContract) {
    const {
      creditScoreGroupIds = [],
      loanTerm,
      loanBalanceStart,
      loanBalanceEnd,
      loanAmountStart,
      loanAmountEnd,
      startDate,
      endDate,
    } = request.qs()

    let allLoan = 0
    let active = 0
    let paid = 0
    let grace = 0
    let overdue = 0

    let getAllLoanHolderUser = null as any

    getAllLoanHolderUser = LoanPeriodTracker.query()
      .preload('user', (subQuery) => {
        subQuery
          .select('id', 'email', 'username')
          .preload('farmer', (subQuery) => {
            subQuery.select('id', 'user_id', 'first_name', 'last_name', 'middle_name')
          })
          .preload('wallet')
      })
      .preload('topupRequest', (subQuery) => {
        subQuery
          .select(
            'id',
            'approved_by_id',
            'credit_score_group_id',
            'total_loan_amount',
            'payment_status',
            'due_at',
            'loan_term'
          )
          .preload('approvedBy', (subQuery) => {
            subQuery.select('id', 'email', 'username').preload('admin').preload('finance')
          })
          .preload('creditScoreGroup')
      })
      .where('status', LoanPeriodTrackerStatus.ONGOING)

    if (creditScoreGroupIds && creditScoreGroupIds.length > 0) {
      getAllLoanHolderUser = getAllLoanHolderUser.whereHas('topupRequest', (subQuery) => {
        subQuery.whereIn('creditScoreGroupId', creditScoreGroupIds)
      })
    }

    if (startDate && endDate) {
      const start = DateTime.fromISO(startDate, { zone: 'Asia/Manila' }).startOf('day').toUTC()
      const end = DateTime.fromISO(endDate, { zone: 'Asia/Manila' }).endOf('day').toUTC()

      getAllLoanHolderUser = getAllLoanHolderUser.whereBetween('updatedAt', [
        start.toSQL(),
        end.toSQL(),
      ])
    }

    if (loanTerm) {
      getAllLoanHolderUser = getAllLoanHolderUser.whereHas('topupRequest', (subQuery) => {
        subQuery.where('loanTerm', '<=', loanTerm)
      })
    }

    if ((loanAmountStart || Number(loanAmountStart ?? 0) === 0) && loanAmountEnd) {
      getAllLoanHolderUser = getAllLoanHolderUser.whereHas('topupRequest', (subQuery) => {
        subQuery.whereBetween('totalLoanAmount', [Number(loanAmountStart), Number(loanAmountEnd)])
      })
    }

    if ((loanBalanceStart || Number(loanBalanceStart ?? 0) === 0) && loanBalanceEnd) {
      getAllLoanHolderUser = getAllLoanHolderUser.whereHas('user', (subQuery) => {
        subQuery.whereHas('wallet', (subQuery) => {
          subQuery.whereBetween('payment', [Number(loanBalanceStart), Number(loanBalanceEnd)])
        })
      })
    }

    getAllLoanHolderUser = await getAllLoanHolderUser.orderBy('id', 'desc')

    for (const loanHolderUser of getAllLoanHolderUser) {
      allLoan++

      if (loanHolderUser.afterLoanEndAt) {
        paid++
      } else if (
        loanHolderUser.afterLoanStartAt.toMillis() <= DateTime.local().toMillis() &&
        loanHolderUser.gracePeriodEndAt.toMillis() >= DateTime.local().toMillis()
      ) {
        grace++
      } else if (loanHolderUser.afterLoanStartAt.toMillis() > DateTime.local().toMillis()) {
        active++
      } else {
        overdue++
      }
    }

    return response.json({
      status: 1,
      data: {
        allLoan,
        paid,
        active,
        overdue,
        grace,
      },
    })
  }

  public async loanHolderViewAll({ request, response }: HttpContextContract) {
    const {
      creditScoreGroupIds = [],
      paymentStatuses = [],
      loanTerm,
      loanBalanceStart,
      loanBalanceEnd,
      loanAmountStart,
      loanAmountEnd,
      page = 1,
      pageSize = 10,
      startDate,
      endDate,
    } = request.qs()

    let getAllLoanHolderUser = null as any

    getAllLoanHolderUser = LoanPeriodTracker.query()
      .preload('user', (subQuery) => {
        subQuery
          .select('id', 'email', 'username')
          .preload('farmer', (subQuery) => {
            subQuery.select('id', 'user_id', 'first_name', 'last_name', 'middle_name')
          })
          .preload('wallet')
      })
      .preload('topupRequest', (subQuery) => {
        subQuery
          .select(
            'id',
            'approved_by_id',
            'credit_score_group_id',
            'total_loan_amount',
            'payment_status',
            'due_at',
            'loan_term'
          )
          .preload('approvedBy', (subQuery) => {
            subQuery.select('id', 'email', 'username').preload('admin').preload('finance')
          })
          .preload('creditScoreGroup')
      })
      .where('status', LoanPeriodTrackerStatus.ONGOING)

    if (creditScoreGroupIds && creditScoreGroupIds.length > 0) {
      getAllLoanHolderUser = getAllLoanHolderUser.whereHas('topupRequest', (subQuery) => {
        subQuery.whereIn('creditScoreGroupId', creditScoreGroupIds)
      })
    }

    if (startDate && endDate) {
      const start = DateTime.fromISO(startDate, { zone: 'Asia/Manila' }).startOf('day').toUTC()
      const end = DateTime.fromISO(endDate, { zone: 'Asia/Manila' }).endOf('day').toUTC()

      getAllLoanHolderUser = getAllLoanHolderUser.whereBetween('updatedAt', [
        start.toSQL(),
        end.toSQL(),
      ])
    }

    if (loanTerm) {
      getAllLoanHolderUser = getAllLoanHolderUser.whereHas('topupRequest', (subQuery) => {
        subQuery.where('loanTerm', '<=', loanTerm)
      })
    }

    if (paymentStatuses && paymentStatuses.length > 0) {
      getAllLoanHolderUser = getAllLoanHolderUser.where((subQuery) => {
        if (paymentStatuses.includes('ACTIVE')) {
          subQuery.where((subQuery) => {
            subQuery
              .whereNull('afterLoanEndAt')
              .where('afterLoanStartAt', '>', DateTime.local().toSQL({ includeOffset: false })!)
          })
        }

        if (paymentStatuses.includes('PAID')) {
          subQuery.orWhereNotNull('afterLoanEndAt')
        }

        if (paymentStatuses.includes('GRACE')) {
          subQuery.orWhere((subQuery) => {
            subQuery
              .whereNull('afterLoanEndAt')
              .where('afterLoanStartAt', '<', DateTime.local().toSQL({ includeOffset: false })!)
              .where('gracePeriodEndAt', '>', DateTime.local().toSQL({ includeOffset: false })!)
          })
        }

        if (paymentStatuses.includes('OVERDUE')) {
          subQuery.orWhere((subQuery) => {
            subQuery
              .whereNull('afterLoanEndAt')
              .where('gracePeriodEndAt', '<', DateTime.local().toSQL({ includeOffset: false })!)
          })
        }
      })
    }

    if ((loanAmountStart || Number(loanAmountStart ?? 0) === 0) && loanAmountEnd) {
      getAllLoanHolderUser = getAllLoanHolderUser.whereHas('topupRequest', (subQuery) => {
        subQuery.whereBetween('totalLoanAmount', [Number(loanAmountStart), Number(loanAmountEnd)])
      })
    }

    if ((loanBalanceStart || Number(loanBalanceStart ?? 0) === 0) && loanBalanceEnd) {
      getAllLoanHolderUser = getAllLoanHolderUser.whereHas('user', (subQuery) => {
        subQuery.whereHas('wallet', (subQuery) => {
          subQuery.whereBetween('payment', [Number(loanBalanceStart), Number(loanBalanceEnd)])
        })
      })
    }

    getAllLoanHolderUser = await getAllLoanHolderUser.paginate(page, pageSize)

    return response.json({
      status: 1,
      data: getAllLoanHolderUser,
    })
  }

  public async loanApplicantHolderViewAll({ request, response }: HttpContextContract) {
    const {
      page = 1,
      pageSize = 10,
      startDate,
      endDate,
      search,
      stage,
      isCompleted,
      isWithdrawn,
      requirements = [],
    } = request.qs()

    const getAllLoanApplicants = await LoanApplicationSubmission.query()
      .if(startDate && endDate, (query) => {
        const start = DateTime.fromISO(startDate, { zone: 'Asia/Manila' }).startOf('day').toUTC()
        const end = DateTime.fromISO(endDate, { zone: 'Asia/Manila' }).endOf('day').toUTC()

        query.whereBetween('createdAt', [start.toSQL()!, end.toSQL()!])
      })
      .if(search, (query) => {
        query.whereHas('user', (subQuery) => {
          subQuery.whereILike('email', `%${search}%`).orWhereHas('farmer', (subQuery) => {
            subQuery.whereILike('firstName', `%${search}%`).orWhereILike('lastName', `%${search}%`)
          })
        })
      })
      .if(stage, (query) => {
        query
          .where('loanApplicationStage', stage)
          .whereNotIn('status', [
            LoanApplicationSubmissionStatus.REJECTED,
            LoanApplicationSubmissionStatus.APPROVE,
          ])
      })
      .if(isCompleted === 'true', (query) => {
        query.where('status', LoanApplicationSubmissionStatus.APPROVE)
      })
      .if(isWithdrawn === 'true', (query) => {
        query.where('status', LoanApplicationSubmissionStatus.REJECTED)
      })
      .if(requirements.length > 0, (query) => {
        query.whereHas('user', (subQuery) => {
          subQuery.whereHas('farmer', (subQuery) => {
            subQuery.whereHas('farmerLoanRequirements', (subQuery) => {
              subQuery.whereHas('farmerLoanRequirementItems', (subQuery) => {
                subQuery
                  .whereIn('name', requirements)
                  .where('isCompleted', EFarmerLoanRequirementItemStatus.PENDING)
              })
            })
          })
        })
      })
      .whereNotIn('status', [LoanApplicationSubmissionStatus.AVAILED])
      .preload('approvedBy', (subQuery) => {
        subQuery
          .select('id', 'email', 'username')
          .preload('admin', (subQuery) => {
            subQuery.select('id', 'user_id', 'first_name', 'last_name')
          })
          .preload('finance', (subQuery) => {
            subQuery.select('id', 'user_id', 'first_name', 'last_name')
          })
      })
      .preload('processedBy', (subQuery) => {
        subQuery
          .select('id', 'email', 'username')
          .preload('admin', (subQuery) => {
            subQuery.select('id', 'user_id', 'first_name', 'last_name')
          })
          .preload('finance', (subQuery) => {
            subQuery.select('id', 'user_id', 'first_name', 'last_name')
          })
      })
      .preload('approvedCreditScoreGroup')
      .preload('financingCompanies', (subQuery) => {
        subQuery.preload('creditScoreGroup')
      })
      .preload('user', (subQuery) => {
        subQuery.select('id', 'email', 'username').preload('farmer', (subQuery) => {
          subQuery.select(
            'id',
            'user_id',
            'first_name',
            'last_name',
            'middle_name',
            'address',
            'permanent_address'
          )
        })
      })
      .orderBy('id', 'desc')
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: getAllLoanApplicants,
    })
  }

  public async loanApplicantHolderDashboard({ response, request }: HttpContextContract) {
    const { startDate, endDate } = request.qs()
    let all = 0
    let stageOne = 0
    let stageTwo = 0
    let stageThree = 0
    let stageFour = 0
    let completed = 0
    let approved = 0
    let pending = 0
    let rejected = 0
    let withdrawn = 0

    const getAllLoanApplicants = await LoanApplicationSubmission.query()
      .whereNotIn('status', [LoanApplicationSubmissionStatus.AVAILED])
      .if(startDate && endDate, (query) => {
        const start = DateTime.fromISO(startDate, { zone: 'Asia/Manila' }).startOf('day').toUTC()
        const end = DateTime.fromISO(endDate, { zone: 'Asia/Manila' }).endOf('day').toUTC()

        query.whereBetween('createdAt', [start.toSQL()!, end.toSQL()!])
      })
      .orderBy('loanApplicationStage', 'asc')

    for (const loanApplicant of getAllLoanApplicants) {
      if (loanApplicant.status === LoanApplicationSubmissionStatus.PENDING) {
        all++

        switch (loanApplicant.loanApplicationStage) {
          case 1:
            stageOne++
            break
          case 2:
            stageTwo++
            break
          case 3:
            stageThree++
            break
          case 4:
            stageFour++
            break
          default:
            break
        }
      }
      if (loanApplicant.status === LoanApplicationSubmissionStatus.APPROVE) {
        completed++
      }

      if (loanApplicant.status === LoanApplicationSubmissionStatus.REJECTED) {
        withdrawn++
      }

      switch (loanApplicant.status) {
        case LoanApplicationSubmissionStatus.APPROVE:
          approved++
          break
        case LoanApplicationSubmissionStatus.PENDING:
          pending++
          break
        case LoanApplicationSubmissionStatus.REJECTED:
          rejected++
          break
        default:
          break
      }
    }

    return response.json({
      status: 1,
      data: {
        all,
        stageOne,
        stageTwo,
        stageThree,
        stageFour,
        completed,
        approved,
        pending,
        rejected,
        withdrawn,
      },
    })
  }

  public async loanApplicantHolderViewDetail({ response, request }: HttpContextContract) {
    const { loanApplicationSubmissionId } = request.params()

    const getAllLoanApplicantDetail = await LoanApplicationSubmission.query()
      .preload('approvedBy', (subQuery) => {
        subQuery
          .select('id', 'email', 'username')
          .preload('admin', (subQuery) => {
            subQuery.select('id', 'user_id', 'first_name', 'last_name')
          })
          .preload('finance', (subQuery) => {
            subQuery.select('id', 'user_id', 'first_name', 'last_name')
          })
      })
      .preload('processedBy', (subQuery) => {
        subQuery
          .select('id', 'email', 'username')
          .preload('admin', (subQuery) => {
            subQuery.select('id', 'user_id', 'first_name', 'last_name')
          })
          .preload('finance', (subQuery) => {
            subQuery.select('id', 'user_id', 'first_name', 'last_name')
          })
      })
      .preload('approvedCreditScoreGroup')
      .preload('financingCompanies', (subQuery) => {
        subQuery.preload('creditScoreGroup')
      })
      .preload('user', (subQuery) => {
        subQuery.select('id', 'email', 'username').preload('farmer', (subQuery) => {
          subQuery.select('id', 'user_id', 'first_name', 'last_name', 'middle_name')
        })
      })
      .where('id', loanApplicationSubmissionId)
      .first()

    return response.json({
      status: 1,
      data: getAllLoanApplicantDetail,
    })
  }

  public async approvedLoanApplicantHolderUserViewAll({ response }: HttpContextContract) {
    const findUsers = await User.query()
      .preload('farmer', (subQuery) => {
        subQuery
          .preload('farmerInfo')
          .preload('cropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('subCropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('familyProfiles')
          .preload('farmerInsurance')
          .preload('farmerDataPrivacy')
          .preload('farmerCreditScore')
          .preload('farmerVehicles')
          .preload('governmentIdentifications')
          .preload('realProperties')
          .preload('farmerBankDetails')
          .preload('farmerCharacterReferences')
          .preload('farmerVouchLeaders')
          .preload('farmerVouchMaos')
          .preload('farmerReferrers')
          .preload('farmerUtilities')
          .preload('farmerMobileDevice')
          .preload('chemicals', (subQuery) => {
            subQuery.preload('chemical', (subQuery) => {
              subQuery
                .preload('chemicalSubcategory')
                .preload('chemicalModeOfAction')
                .preload('chemicalActiveIngredients', (subQuery) => {
                  subQuery.preload('chemicalActiveIngredient')
                })
            })
          })
          .preload('seeds', (subQuery) => {
            subQuery.preload('seed')
          })
          .preload('seedSubcategories', (subQuery) => {
            subQuery.preload('seedSubcategory')
          })
          .preload('fertilizers', (subQuery) => {
            subQuery.preload('fertilizer')
          })
          .preload('farmerLandbankRequirements', (subQuery) => {
            subQuery.preload('processedBy')
          })
          .preload('farmerLoanRequirements')
      })
      .preload('wallet')
      .where('user_type', UserType.FARMER)
      .whereHas('loanApplicationSubmissions', (subQuery) => {
        subQuery.where('status', LoanApplicationSubmissionStatus.APPROVE)
      })
      .orderBy('id', 'desc')

    return response.json({
      status: 1,
      data: findUsers,
    })
  }

  public async approveLoanApplicant({ auth, response, request }: HttpContextContract) {
    const { user } = auth

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid user',
      })
    }

    const { loanApplicationSubmissionId, creditScoreGroupId } = request.body()

    if (!creditScoreGroupId) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'creditScoreGroupId not found!',
      })
    }

    if (!loanApplicationSubmissionId) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'loanApplicationSubmissionId not found!',
      })
    }

    const getLoanApplicantDetail = await LoanApplicationSubmission.find(loanApplicationSubmissionId)

    if (!getLoanApplicantDetail) {
      response.status(404)
      return response.json({
        status: 0,
        message: 'Loan Application not found!',
      })
    }

    if (getLoanApplicantDetail.status !== LoanApplicationSubmissionStatus.PENDING) {
      response.status(404)
      return response.json({
        status: 0,
        message: 'Loan Application status is not PENDING!',
      })
    }

    const findCreditScoreGroup = await LoanApplicationSubmissionCreditScoreGroupPivot.query()
      .where('creditScoreGroupId', creditScoreGroupId)
      .where('loanApplicationSubmissionId', loanApplicationSubmissionId)
      .first()

    if (!findCreditScoreGroup) {
      response.status(404)
      return response.json({
        status: 0,
        message: 'Credit Score Group not found!',
      })
    }

    getLoanApplicantDetail.creditScoreGroupId = findCreditScoreGroup.creditScoreGroupId
    getLoanApplicantDetail.status = LoanApplicationSubmissionStatus.APPROVE
    getLoanApplicantDetail.approvedById = user.id
    getLoanApplicantDetail.approvedAt = DateTime.local()

    await getLoanApplicantDetail.save()

    return response.json({
      status: 1,
      message: 'Loan Application has been approved',
    })
  }

  public async rejectLoanApplicant({ auth, response, request }: HttpContextContract) {
    const { user } = auth

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid user',
      })
    }

    const { loanApplicationSubmissionId } = request.body()

    const getLoanApplicantDetail = await LoanApplicationSubmission.find(loanApplicationSubmissionId)

    if (!getLoanApplicantDetail) {
      response.status(404)
      return response.json({
        status: 0,
        message: 'Loan Application not found!',
      })
    }

    const findFarmer = await Farmer.findBy('userId', getLoanApplicantDetail.userId)

    if (!findFarmer) {
      response.status(404)
      return response.json({
        status: 0,
        message: 'Farmer not found!',
      })
    }

    if (getLoanApplicantDetail.status !== LoanApplicationSubmissionStatus.PENDING) {
      response.status(404)
      return response.json({
        status: 0,
        message: 'Loan Application status is not PENDING!',
      })
    }

    findFarmer.hasSubmittedLoanApplication = HasSubmittedLoanApplication.NO

    await findFarmer.save()

    getLoanApplicantDetail.status = LoanApplicationSubmissionStatus.REJECTED

    await getLoanApplicantDetail.save()

    return response.json({
      status: 1,
      message: 'Loan Application has been rejected',
    })
  }
}
