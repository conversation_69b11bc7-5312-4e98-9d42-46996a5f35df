import { DateTime } from 'luxon'
import { afterFetch, BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'
import Farmer from './Farmer'
import User from './User'
import FarmerLoanRequirement, {
  TFarmerLoanRequirementStageOne,
  TFarmerLoanRequirementStageTwo,
  TFarmerLoanRequirementStageThree,
  TFarmerLoanRequirementStageFour,
  EFarmerLoanRequirementItemStage,
} from './FarmerLoanRequirement'
import KITA_CONFIG from 'Config/kita'

export enum EFarmerLoanRequirementItemStatus {
  PENDING = 0,
  COMPLETED = 1,
}

export default class FarmerLoanRequirementItem extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public farmerId: number

  @column()
  public processedById: number

  @column()
  public farmerLoanRequirementId: number

  @column()
  public name:
    | TFarmerLoanRequirementStageOne
    | TFarmerLoanRequirementStageTwo
    | TFarmerLoanRequirementStageThree
    | TFarmerLoanRequirementStageFour

  @column()
  public stage: EFarmerLoanRequirementItemStage

  @column()
  public attachment?: string | null

  @column()
  public notes?: string | null

  @column()
  public isCompleted?: EFarmerLoanRequirementItemStatus | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Farmer)
  public farmer: BelongsTo<typeof Farmer>

  @belongsTo(() => User, {
    foreignKey: 'processedById',
  })
  public processedBy: BelongsTo<typeof User>

  @belongsTo(() => FarmerLoanRequirement)
  public farmerLoanRequirement: BelongsTo<typeof FarmerLoanRequirement>

  @afterFetch()
  public static afterFetchHook(items: FarmerLoanRequirementItem[]) {
    items.forEach((item) => {
      if (item.attachment) {
        const filePath = item.attachment
        const fileName = filePath.split('/')[filePath.split('/').length - 1]

        item.attachment = `${KITA_CONFIG.API_URL}/uploads/users/loanrequirements/${fileName}`
      }
    })
  }
}
