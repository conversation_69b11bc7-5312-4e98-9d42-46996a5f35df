/* eslint-disable @typescript-eslint/naming-convention */
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Farmer from 'App/Models/Farmer'
import User from 'App/Models/User'
import Ws from 'App/Services/Ws'
import Env from '@ioc:Adonis/Core/Env'

export default class ApisController {
  public async weight({ request, response }: HttpContextContract) {
    const { weight, type } = request.body()

    if (!['in', 'out'].includes(type)) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid type. Only in or out!',
      })
    }

    Ws.io.to(`weighing-encoder-user`).emit(`weight:${type}`, {
      status: 1,
      data: weight,
    })

    return response.json({
      status: 1,
      message: `Weight sent to ${type}`,
    })
  }

  public async rfid({ request, response }: HttpContextContract) {
    const { rfidNumber, type } = request.body()

    if (!['in', 'out'].includes(type)) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid type. Only in or out!',
      })
    }

    const findUser = await User.findBy('rfidNumber', rfidNumber)

    if (!findUser) {
      Ws.io.to(`rfid-encoder-user`).emit(`rfid:in`, {
        status: 0,
        message: 'User not found!',
      })

      Ws.io.to(`rfid-encoder-user`).emit(`rfid:out`, {
        status: 0,
        message: 'User not found!',
      })

      response.status(400)
      return response.json({
        status: 0,
        message: 'User not found!',
      })
    }

    const findFarmers = await Farmer.query()
      .preload('farmerInfo')
      .preload('cropsPlanted', (subQuery) => {
        subQuery.preload('crop')
      })
      .preload('farmerVehicles')
      .where('userId', findUser.id)
      .first()

    if (!findFarmers) {
      Ws.io.to(`rfid-encoder-user`).emit(`rfid:in`, {
        status: 0,
        message: 'Farmer not found!',
      })

      Ws.io.to(`rfid-encoder-user`).emit(`rfid:out`, {
        status: 0,
        message: 'Farmer not found!',
      })

      response.status(400)
      return response.json({
        status: 0,
        message: 'Farmer not found!',
      })
    }

    Ws.io.to(`rfid-encoder-user`).emit(`rfid:in`, {
      status: 1,
      data: findFarmers,
    })

    Ws.io.to(`rfid-encoder-user`).emit(`rfid:out`, {
      status: 1,
      data: findFarmers,
    })

    return response.json({
      status: 1,
      message: `Rfid sent to ${type}`,
    })
  }

  public async platenumber({ request, response }: HttpContextContract) {
    const { plateNumber, type } = request.body()

    if (!['in', 'out'].includes(type)) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid type. Only in or out!',
      })
    }

    Ws.io.to(`weighing-encoder-user`).emit(`platenumber:${type}`, {
      status: 1,
      data: plateNumber,
    })

    return response.json({
      status: 1,
      message: `Plate number sent to ${type}`,
    })
  }

  public async platenumber_test({ request, response }: HttpContextContract) {
    const body = request.body()

    if (Env.get('APNR_ENTRACE_DEVICE_ID') === body.Picture?.SnapInfo?.DeviceID) {
      Ws.io.to(`weighing-encoder-user`).emit(`platenumber:in`, {
        status: 1,
        data: body.Picture?.Plate?.PlateNumber,
        image: body.Picture?.CutoutPic?.Content,
        device: body.Picture?.SnapInfo?.DeviceID,
      })
    }

    if (Env.get('APNR_EXIT_DEVICE_ID') === body.Picture?.SnapInfo?.DeviceID) {
      Ws.io.to(`weighing-encoder-user`).emit(`platenumber:out`, {
        status: 1,
        data: body.Picture?.Plate?.PlateNumber,
        image: body.Picture?.CutoutPic?.Content,
        device: body.Picture?.SnapInfo?.DeviceID,
      })
    }

    Ws.io.to(`weighing-encoder-user`).emit(`test`, {
      status: 1,
      data: body.Picture,
    })

    return response.json({
      Result: true,
      DeviceID: body?.Picture?.SnapInfo?.DeviceID,
    })
  }

  public async keepalive({ request, response }: HttpContextContract) {
    Ws.io.to(`weighing-encoder-user`).emit(`keepalive`, {
      status: 1,
      data: request.body(),
    })

    return response.json(request.body())
  }

  public async qr_scan({ request, response }: HttpContextContract) {
    const { qr_code, type } = request.body()

    if (!['in', 'out'].includes(type)) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid type. Only in or out!',
      })
    }

    Ws.io.to(`qrscanning-encoder-user`).emit(`qrscan:${type}`, {
      status: 1,
      data: qr_code,
    })

    return response.json({
      status: 1,
      message: `QR code sent to ${type}`,
    })
  }
}
