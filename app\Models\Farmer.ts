import { DateTime } from 'luxon'
import {
  BaseModel,
  belongsTo,
  BelongsTo,
  column,
  hasOne,
  HasOne,
  hasMany,
  HasMany,
} from '@ioc:Adonis/Lucid/Orm'
import User from './User'
import FarmerInfo from './FarmerInfo'
import FarmerCropPivot from './FarmerCropPivot'
import FarmerFertilizerPivot from './FarmerFertilizerPivot'
import FarmerSeedPivot from './FarmerSeedPivot'
import FarmerGovernmentIdentification from './FarmerGovernmentIdentification'
import FarmerRealProperty from './FarmerRealProperty'
import FarmerFamilyProfile from './FarmerFamilyProfile'
import FarmerVehicle from './FarmerVehicle'
import FarmerChemicalPivot from './FarmerChemicalPivot'
import FarmerInsurance from './FarmerInsurance'
import TradingPostLog from './TradingPostLog'
import FarmerBankingDetail from './FarmerBankingDetail'
import CreditScoring from './CreditScoring'
import FarmerCharacterReference from './FarmerCharacterReference'
import FarmerVouchLeader from './FarmerVouchLeader'
import FarmerVouchMao from './FarmerVouchMao'
import FarmerTransaction from './FarmerTransaction'
import FarmerSeedSubcategoryPivot from './FarmerSeedSubcategoryPivot'
import FarmerSubCropPivot from './FarmerSubCropPivot'
import FarmerReferrer from './FarmerReferrer'
import FarmerMobileDevice from './FarmerMobileDevice'
import FarmerUtility from './FarmerUtility'
import FarmerLandbankRequirement from './FarmerLandbankRequirement'
import FarmerDataPrivacy from './FarmerDataPrivacy'
import FarmerLoanRequirement from './FarmerLoanRequirement'

export enum FarmerGender {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHERS = 'OTHERS',
}

export enum FarmerCivilStatus {
  SINGLE = 'SINGLE',
  MARRIED = 'MARRIED',
  SEPARATED = 'SEPARATED',
  DIVORCED = 'DIVORCED',
  WIDOWED = 'WIDOWED',
}

export enum FarmerEducationalAttainment {
  NONE = 'NONE',
  ELEMENTARY = 'ELEMENTARY',
  HIGHSCHOOL = 'HIGHSCHOOL',
  VOCATIONAL = 'VOCATIONAL',
  COLLEGE = 'COLLEGE',
  POSTGRADUATE = 'POSTGRADUATE',
}

export enum FarmerOccupation {
  UNEMPLOYED = 'UNEMPLOYED',
  GOVERNMENT_EMPLOYEE = 'GOVERNMENT EMPLOYEE',
  PRIVATE_EMPLOYEE = 'PRIVATE EMPLOYEE',
  SELF_EMPLOYED_PROFESSIONAL = 'SELF EMPLOYED PROFESSIONAL',
  SELF_EMPLOYED_NONPROFESSIONAL = 'SELF EMPLOYED NONPROFESSIONAL',
  BUSINESS_PERSON = 'BUSINESS PERSON',
  CHURCH_SERVANTS = 'CHURCH SERVANTS',
  OFW = 'OFW',
  RETIREE = 'RETIREE',
  HOUSEWIFE = 'HOUSEWIFE',
  STUDENT = 'STUDENT',
  FARMER = 'FARMER',
  LABORER = 'LABORER',
}

export enum FarmerOccupationStatus {
  TEMPORARY = 'TEMPORARY',
  PERMANENT = 'PERMANENT',
  CONTRACTUAL = 'CONTRACTUAL',
}

export enum EducationalGraduateStatus {
  NO = 0,
  YES = 1,
}

export enum HasLoan {
  NO = 0,
  YES = 1,
}

export enum HasSubmittedLoanApplication {
  NO = 0,
  YES = 1,
}

export default class Farmer extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public userId: number

  @column()
  public firstName: string | null

  @column()
  public middleName: string | null

  @column()
  public lastName: string | null

  @column.date()
  public birthDate: DateTime | null

  @column()
  public placeOfBirth: string | null

  @column()
  public religion: string | null

  @column()
  public gender: FarmerGender | null

  @column()
  public civilStatus: FarmerCivilStatus | null

  @column()
  public spouseName: string | null

  @column()
  public spouseMobileNumber: string | null

  @column()
  public height: number | null

  @column()
  public weight: number | null

  @column()
  public mobileNumber: string | null

  @column()
  public address: string | null

  @column()
  public addressHouseNumber: string | null

  @column()
  public addressStreet: string | null

  @column()
  public addressProvince: string | null

  @column()
  public addressCity: string | null

  @column()
  public addressBarangay: string | null

  @column()
  public addressZipCode: string | null

  @column()
  public educationalAttainment: FarmerEducationalAttainment | null

  @column()
  public educationalIsGraduate: EducationalGraduateStatus | null

  @column()
  public educationalDegree?: string | null

  @column()
  public occupation: FarmerOccupation | null

  @column()
  public occupationTitle?: string | null

  @column()
  public occupationStatus?: FarmerOccupationStatus | null

  @column()
  public occupationEmployerName?: string | null

  @column()
  public occupationEmployerAddress?: string | null

  @column()
  public occupationBusinessName?: string | null

  @column()
  public occupationBusinessAddress?: string | null

  @column()
  public occupationBusinessContact?: string | null

  @column()
  public occupationAnnualIncome?: number | null

  @column()
  public skillsFarming: string | null

  @column()
  public skillsFishing: number | null

  @column()
  public skillsLivestock: string | null

  @column()
  public skillsConstruction: string | null

  @column()
  public skillsProcessing: string | null

  @column()
  public skillsServicing: string | null

  @column()
  public skillsCraft: string | null

  @column()
  public skillsOthers: string | null

  @column()
  public vehicleOwned: string | null

  @column()
  public status: number | null

  @column({ serializeAs: null })
  public biometric: string | null

  @column()
  public hasBiometric: number | null

  @column({ serializeAs: null })
  public facialRecognition: string | null

  @column()
  public hasFacialRecognition: number | null

  @column()
  public qrCode: string | null

  @column()
  public hasLoan: HasLoan | null

  @column()
  public hasSubmittedLoanApplication: HasSubmittedLoanApplication | null

  @column()
  public permanentAddress: string | null

  @column()
  public permanentAddressHouseNumber: string | null

  @column()
  public permanentAddressStreet: string | null

  @column()
  public permanentAddressProvince: string | null

  @column()
  public permanentAddressCity: string | null

  @column()
  public permanentAddressBarangay: string | null

  @column()
  public permanentAddressZipCode: string | null

  @column()
  public permanentAddressLengthOfStay: number | null

  @column()
  public addressLengthOfStay: number | null

  @column()
  public sourceOfFunds: string | null

  @column()
  public landbankAccounts: string | null

  @column()
  public telephoneNumber: string | null

  @column()
  public facebookName: string | null

  @column()
  public mothersMaidenName: string | null

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => User)
  public user: BelongsTo<typeof User>

  @hasOne(() => FarmerInfo)
  public farmerInfo: HasOne<typeof FarmerInfo>

  @hasMany(() => FarmerCropPivot)
  public cropsPlanted: HasMany<typeof FarmerCropPivot>

  @hasMany(() => FarmerSubCropPivot)
  public subCropsPlanted: HasMany<typeof FarmerSubCropPivot>

  @hasMany(() => FarmerFertilizerPivot)
  public fertilizers: HasMany<typeof FarmerFertilizerPivot>

  @hasMany(() => FarmerSeedPivot)
  public seeds: HasMany<typeof FarmerSeedPivot>

  @hasMany(() => FarmerSeedSubcategoryPivot)
  public seedSubcategories: HasMany<typeof FarmerSeedSubcategoryPivot>

  @hasMany(() => FarmerChemicalPivot)
  public chemicals: HasMany<typeof FarmerChemicalPivot>

  @hasMany(() => FarmerGovernmentIdentification)
  public governmentIdentifications: HasMany<typeof FarmerGovernmentIdentification>

  @hasMany(() => FarmerRealProperty)
  public realProperties: HasMany<typeof FarmerRealProperty>

  @hasMany(() => FarmerFamilyProfile)
  public familyProfiles: HasMany<typeof FarmerFamilyProfile>

  @hasMany(() => FarmerVehicle)
  public farmerVehicles: HasMany<typeof FarmerVehicle>

  @hasMany(() => FarmerBankingDetail)
  public farmerBankDetails: HasMany<typeof FarmerBankingDetail>

  @hasMany(() => FarmerCharacterReference)
  public farmerCharacterReferences: HasMany<typeof FarmerCharacterReference>

  @hasMany(() => FarmerVouchLeader)
  public farmerVouchLeaders: HasMany<typeof FarmerVouchLeader>

  @hasMany(() => FarmerVouchMao)
  public farmerVouchMaos: HasMany<typeof FarmerVouchMao>

  @hasMany(() => FarmerReferrer)
  public farmerReferrers: HasMany<typeof FarmerReferrer>

  @hasOne(() => FarmerInsurance)
  public farmerInsurance: HasOne<typeof FarmerInsurance>

  @hasOne(() => CreditScoring)
  public farmerCreditScore: HasOne<typeof CreditScoring>

  @hasOne(() => FarmerTransaction)
  public farmerTransaction: HasOne<typeof FarmerTransaction>

  @hasMany(() => TradingPostLog)
  public tradingPostLogs: HasMany<typeof TradingPostLog>

  @hasOne(() => FarmerMobileDevice)
  public farmerMobileDevice: HasOne<typeof FarmerMobileDevice>

  @hasMany(() => FarmerUtility)
  public farmerUtilities: HasMany<typeof FarmerUtility>

  @hasMany(() => FarmerLandbankRequirement)
  public farmerLandbankRequirements: HasMany<typeof FarmerLandbankRequirement>

  @hasOne(() => FarmerDataPrivacy)
  public farmerDataPrivacy: HasOne<typeof FarmerDataPrivacy>

  @hasMany(() => FarmerLoanRequirement)
  public farmerLoanRequirements: HasMany<typeof FarmerLoanRequirement>
}
