import { Exception } from '@adonisjs/core/build/standalone'
import Bull from '@ioc:Rocketseat/Bull'
import ProcessCreditScoreComputationJob from 'App/Jobs/ProcessCreditScoreComputationJob'
import CreditScoreConfiguration from 'App/Models/CreditScoreConfiguration'
import CreditScoreGroup, { CreditScoreGroupStatus } from 'App/Models/CreditScoreGroup'
import FarmerCreditScore from 'App/Models/FarmerCreditScore'
import FarmerCreditScoreHistory from 'App/Models/FarmerCreditScoreHistory'
import User from 'App/Models/User'
import CREDIT_SCORING_CONFIG_TEMPLATE from 'Config/credit-scoring'
import { calculateDetailedCreditScore } from './CreditScoreCalculator'

export const triggerCreditScoreComputation = async (userId: number) => {
  await Bull.add(
    new ProcessCreditScoreComputationJob().key,
    {
      user_id: userId,
    },
    {
      attempts: 5,
    }
  )
}

export const creditScoreRecomputation = async (userId: number) => {
  try {
    const findUser = await User.query()
      .preload('farmer', (subQuery) => {
        subQuery
          .preload('farmerInfo')
          .preload('cropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('user')
          .preload('familyProfiles')
          .preload('farmerInsurance')
          .preload('farmerDataPrivacy')
          .preload('farmerCreditScore')
          .preload('farmerVehicles')
          .preload('governmentIdentifications')
          .preload('realProperties')
          .preload('farmerBankDetails')
          .preload('farmerCharacterReferences')
          .preload('farmerVouchLeaders')
          .preload('farmerVouchMaos')
          .preload('chemicals', (subQuery) => {
            subQuery.preload('chemical', (subQuery) => {
              subQuery
                .preload('chemicalSubcategory')
                .preload('chemicalModeOfAction')
                .preload('chemicalActiveIngredients', (subQuery) => {
                  subQuery.preload('chemicalActiveIngredient')
                })
            })
          })
          .preload('seeds', (subQuery) => {
            subQuery.preload('seed')
          })
          .preload('fertilizers', (subQuery) => {
            subQuery.preload('fertilizer')
          })
          .preload('farmerLandbankRequirements', (subQuery) => {
            subQuery.preload('processedBy')
          })
          .preload('farmerLoanRequirements')
      })
      .where('id', userId)
      .first()

    if (!findUser) throw new Exception('User not found!')

    const getAllCreditScoreGroup = await CreditScoreGroup.query().where(
      'status',
      CreditScoreGroupStatus.ACTIVE
    )

    const computedScores = [] as { creditScoreGroupId: number; score: number; stage: string }[]

    for (const creditScoreGroup of getAllCreditScoreGroup) {
      const config = await CreditScoreConfiguration.firstOrCreate(
        {
          creditScoreGroupId: creditScoreGroup.id,
        },
        {
          creditScoreGroupId: creditScoreGroup.id,
          creditScoringConfig: CREDIT_SCORING_CONFIG_TEMPLATE,
        }
      )

      const findCreditScore = await FarmerCreditScore.firstOrCreate(
        {
          farmerId: findUser.farmer.id,
          creditScoreGroupId: creditScoreGroup.id,
        },
        {
          farmerId: findUser.farmer.id,
          creditScoreGroupId: creditScoreGroup.id,
          loanCycleNumber: 1,
        }
      )

      const currentStage = findCreditScore.loanStage || 'BEFORE'

      const findCreditScoreHistory = await FarmerCreditScoreHistory.firstOrCreate(
        {
          farmerId: findUser.farmer.id,
          creditScoreGroupId: creditScoreGroup.id,
          loanCycleNumber: findCreditScore.loanCycleNumber,
        },
        {
          farmerId: findUser.farmer.id,
          creditScoreGroupId: creditScoreGroup.id,
          loanCycleNumber: findCreditScore.loanCycleNumber,
        }
      )

      if (!config) throw new Exception('Credit score configuration not found!')

      const result = await calculateDetailedCreditScore(
        findUser,
        findUser?.farmer,
        config.creditScoringConfig,
        findCreditScore
      )

      if (currentStage === 'BEFORE') {
        findCreditScore.beforeStageCreditScoreSummary = result.stageScore
        findCreditScore.beforeStageCreditScore = result.stageScore.computedScore

        findCreditScoreHistory.beforeStageCreditScoreSummary = result.stageScore
        findCreditScoreHistory.beforeStageCreditScore = result.stageScore.computedScore
      } else if (currentStage === 'DURING') {
        findCreditScore.duringStageCreditScoreSummary = result.stageScore
        findCreditScore.duringStageCreditScore = result.stageScore.computedScore

        findCreditScoreHistory.duringStageCreditScoreSummary = result.stageScore
        findCreditScoreHistory.duringStageCreditScore = result.stageScore.computedScore
      } else if (currentStage === 'AFTER') {
        findCreditScore.afterStageCreditScoreSummary = result.stageScore
        findCreditScore.afterStageCreditScore = result.stageScore.computedScore

        findCreditScoreHistory.afterStageCreditScoreSummary = result.stageScore
        findCreditScoreHistory.afterStageCreditScore = result.stageScore.computedScore
      }

      computedScores.push({
        creditScoreGroupId: creditScoreGroup.id,
        score: result.stageScore.computedScore,
        stage: currentStage,
      })

      await findCreditScore.save()
      await findCreditScoreHistory.save()
    }

    return computedScores
  } catch (error) {
    if (error.response) {
      if (error.response.data) {
        if (error.response.data.error) {
          throw new Exception(error.response.data.error)
        }
        if (error.response.data.errors) {
          throw new Exception(error.response.data.errors.toString())
        }
      }
    } else if (error.request) {
      throw new Exception(error.request)
    } else {
      throw new Exception(error.message)
    }
  }
}
