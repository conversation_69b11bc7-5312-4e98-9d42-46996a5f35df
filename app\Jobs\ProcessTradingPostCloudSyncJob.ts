/* eslint-disable @typescript-eslint/naming-convention */
import { Exception } from '@adonisjs/core/build/standalone'
import { JobContract } from '@ioc:Rocketseat/Bull'
import TradingPostLog, { LogStatus, TradingPostSyncStatus } from 'App/Models/TradingPostLog'
import Ws from 'App/Services/Ws'
import KITA_CONFIG from 'Config/kita'
import axios from 'axios'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessTradingPostCloudSyncJob implements JobContract {
  public key = `${KITA_CONFIG.ENV_TYPE}-ProcessTradingPostCloudSyncJob`

  public async handle(job) {
    const { data } = job
    // Do somethign with you job data
    try {
      const checkLastSync = await TradingPostLog.query()
        .where('status', LogStatus.COMPLETE)
        .where('isSync', TradingPostSyncStatus.NOT_SYNCED)
        .orderBy('id', 'asc')
        .first()

      if (!checkLastSync) {
        Ws.io.to(`tradingpost-syncing`).emit(`ProcessTradingPostCloudSyncJob`, {
          status: 1,
          data: {
            model: 'TradingPostLog',
            message: 'Done',
            total: 0,
            synced: 0,
          },
        })

        return 'Tradingpost data is upto date!'
      }

      const fetchNotSyncTradingPostLog = await TradingPostLog.query()
        .preload('farmer', (subQuery) => {
          subQuery.preload('user').preload('farmerVehicles')
        })
        .preload('processedBy')
        .preload('crops', (subQuery) => {
          subQuery.preload('crop')
        })
        .where('status', LogStatus.COMPLETE)
        .where('isSync', TradingPostSyncStatus.NOT_SYNCED)

      let synced = 0

      Ws.io.to(`tradingpost-syncing`).emit(`ProcessTradingPostCloudSyncJob`, {
        status: 1,
        data: {
          model: 'TradingPostLog',
          message: 'In Progress',
          total: fetchNotSyncTradingPostLog.length,
          synced: synced,
        },
      })

      for (const tradingpostEntry of fetchNotSyncTradingPostLog) {
        const _response = await axios
          .post(`${KITA_CONFIG.API_URL}/sync/receiver/tradingpost/single`, {
            secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
            data: tradingpostEntry,
          })
          .then(async (response) => response.data)

        const updateToSync = await TradingPostLog.query()
          .where('id', _response.data)
          .update('isSync', TradingPostSyncStatus.SYNCED)

        console.log(updateToSync)

        synced++

        Ws.io.to(`tradingpost-syncing`).emit(`ProcessTradingPostCloudSyncJob`, {
          status: 1,
          data: {
            model: 'TradingPostLog',
            message: 'In Progress',
            total: fetchNotSyncTradingPostLog.length,
            synced: synced,
          },
        })
      }

      Ws.io.to(`tradingpost-syncing`).emit(`ProcessTradingPostCloudSyncJob`, {
        status: 1,
        data: {
          model: 'TradingPostLog',
          message: 'Done',
          total: fetchNotSyncTradingPostLog.length,
          synced: synced,
        },
      })

      return {
        data,
        result: null,
        total: fetchNotSyncTradingPostLog.length,
        synced: synced,
      }
    } catch (error) {
      if (error.response) {
        if (error.response.data) {
          if (error.response.data.error) {
            throw new Exception(error.response.data.error)
          }
          if (error.response.data.errors) {
            throw new Exception(error.response.data.errors.toString())
          }
        }
      } else if (error.request) {
        throw new Exception(error.request)
      } else {
        throw new Exception(error.message)
      }
    }
  }
}
