/* eslint-disable @typescript-eslint/naming-convention */
import { Exception } from '@adonisjs/core/build/standalone'
import { JobContract } from '@ioc:Rocketseat/Bull'
import CropPrice, { CropPriceSyncStatus } from 'App/Models/CropPrice'
import Ws from 'App/Services/Ws'
import KITA_CONFIG from 'Config/kita'
import axios from 'axios'
import { DateTime } from 'luxon'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessCropPriceCloudSyncJob implements JobContract {
  public key = `${KITA_CONFIG.ENV_TYPE}-ProcessCropPriceCloudSyncJob`

  public async handle(job) {
    const { data } = job
    try {
      const _cropPrices = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/crop/price`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
        })
        .then(async (response) => response.data)

      let synced = 0

      Ws.io.to(`tradingpost-syncing`).emit(`ProcessCropPriceCloudSyncJob`, {
        status: 1,
        data: {
          model: 'CropPrice',
          message: 'In Progress',
          total: _cropPrices.data?.length,
          synced: synced,
        },
      })

      const syncIds = [] as number[]

      for (const _cropPrice of _cropPrices.data) {
        syncIds.push(_cropPrice.id)

        await CropPrice.updateOrCreate(
          { id: _cropPrice.id },
          {
            id: _cropPrice.id,
            cropId: _cropPrice.crop_id,
            sellingPrice: _cropPrice.selling_price || 0,
            productionPrice: _cropPrice.production_price || 0,
            isSync: CropPriceSyncStatus.SYNCED,
            createdAt: DateTime.fromISO(_cropPrice.created_at),
            updatedAt: DateTime.fromISO(_cropPrice.updated_at),
          }
        )

        synced++

        Ws.io.to(`tradingpost-syncing`).emit(`ProcessCropPriceCloudSyncJob`, {
          status: 1,
          data: {
            model: 'CropPrice',
            message: 'In Progress',
            total: _cropPrices.data?.length,
            synced: synced,
          },
        })
      }

      const _validateResponse = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/crop/price/validate`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
          syncIds,
        })
        .then(async (response) => response.data)

      console.log(_validateResponse.message)

      Ws.io.to(`tradingpost-syncing`).emit(`ProcessCropPriceCloudSyncJob`, {
        status: 1,
        data: {
          model: 'CropPrice',
          message: 'Done',
          total: _cropPrices.data?.length,
          synced: synced,
        },
      })

      return {
        data,
        result: _validateResponse.message,
        total: _cropPrices.data?.length,
        synced: synced,
      }
    } catch (error) {
      if (error.response) {
        if (error.response.data) {
          if (error.response.data.error) {
            throw new Exception(error.response.data.error)
          }
          if (error.response.data.errors) {
            throw new Exception(error.response.data.errors.toString())
          }
        }
      } else if (error.request) {
        throw new Exception(error.request)
      } else {
        throw new Exception(error.message)
      }
    }
  }
}
