import Hash from '@ioc:Adonis/Core/Hash'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { triggerCreditScoreComputation } from 'App/Helpers/CreditScoring'
import { triggerLoanRequirementComputation } from 'App/Helpers/LoanRequirement'
import Admin from 'App/Models/Admin'
import Agronomist from 'App/Models/Agronomist'
import AuditLog from 'App/Models/AuditLog'
import CreditScoring, { Status } from 'App/Models/CreditScoring'
import Encoder from 'App/Models/Encoder'
import Farmer, { FarmerCivilStatus } from 'App/Models/Farmer'
import FarmerBankingDetail from 'App/Models/FarmerBankingDetail'
import FarmerCharacterReference from 'App/Models/FarmerCharacterReference'
import FarmerChemicalPivot from 'App/Models/FarmerChemicalPivot'
import FarmerCropPivot from 'App/Models/FarmerCropPivot'
import FarmerFamilyProfile from 'App/Models/FarmerFamilyProfile'
import FarmerFertilizerPivot from 'App/Models/FarmerFertilizerPivot'
import FarmerGovernmentIdentification, {
  GovernmentIdentificationType,
} from 'App/Models/FarmerGovernmentIdentification'
import FarmerInfo, {
  EFertilizerUsed,
  EHasNeedFarmLoan,
  EHasPastFarmLoanPaid,
  EHasPastFarmLoans,
  EIsInterestedToSellAtTradingPost,
  EMemberOfOrganization,
  EPesticideUsed,
  FarmOwnership,
} from 'App/Models/FarmerInfo'
import FarmerInsurance from 'App/Models/FarmerInsurance'
import FarmerLoanRequirementItem, {
  EFarmerLoanRequirementItemStatus,
} from 'App/Models/FarmerLoanRequirementItem'
import FarmerMobileDevice from 'App/Models/FarmerMobileDevice'
import FarmerRealProperty from 'App/Models/FarmerRealProperty'
import FarmerReferrer from 'App/Models/FarmerReferrer'
import FarmerSeedPivot from 'App/Models/FarmerSeedPivot'
import FarmerSeedSubcategoryPivot from 'App/Models/FarmerSeedSubcategoryPivot'
import FarmerSubCropPivot from 'App/Models/FarmerSubCropPivot'
import FarmerTransaction from 'App/Models/FarmerTransaction'
import FarmerUtility from 'App/Models/FarmerUtility'
import FarmerVehicle from 'App/Models/FarmerVehicle'
import FarmerVouchLeader from 'App/Models/FarmerVouchLeader'
import FarmerVouchMao from 'App/Models/FarmerVouchMao'
import FieldRelationOfficer from 'App/Models/FieldRelationOfficer'
import Finance from 'App/Models/Finance'
import LoanPeriodTracker, { LoanPeriodTrackerStatus } from 'App/Models/LoanPeriodTracker'
import Operation from 'App/Models/Operation'
import Sale from 'App/Models/Sale'
import User, { UserStatusType, UserSyncStatus, UserType } from 'App/Models/User'
import CreateUserValidator from 'App/Validators/CreateUserValidator'
import UpdateAgricultureActivityValidator from 'App/Validators/UpdateAgricultureActivityValidator'
import UpdateFarmerValidator from 'App/Validators/UpdateFarmerValidator'
import UpdateUserValidator from 'App/Validators/UpdateUserValidator'

export default class UsersController {
  public async create({ auth, request, response }: HttpContextContract) {
    if (auth.user?.userType !== UserType.SUPERADMIN) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Unauthorized user!',
      })
    }

    const { username, password, firstName, lastName, userType } = await request.validate(
      CreateUserValidator
    )

    const createUser = await User.create({
      email: username,
      password,
      status: UserStatusType.ACTIVATED,
      userType,
    })

    if ([UserType.ENCODER_1, UserType.ENCODER_2].includes(userType)) {
      await Encoder.create({
        firstName,
        lastName,
        userId: createUser.id,
      })
    }

    if ([UserType.OPERATION_1, UserType.OPERATION_2].includes(userType)) {
      await Operation.create({
        firstName,
        lastName,
        userId: createUser.id,
      })
    }

    if ([UserType.SALE_1, UserType.SALE_2].includes(userType)) {
      await Sale.create({
        firstName,
        lastName,
        userId: createUser.id,
      })
    }

    if ([UserType.FINANCE_1, UserType.FINANCE_2].includes(userType)) {
      await Finance.create({
        firstName,
        lastName,
        userId: createUser.id,
      })
    }

    if ([UserType.ADMIN, UserType.SUPERADMIN].includes(userType)) {
      await Admin.create({
        firstName,
        lastName,
        userId: createUser.id,
      })
    }

    if ([UserType.AGRONOMIST, UserType.HEAD_AGRONOMIST].includes(userType)) {
      await Agronomist.create({
        firstName,
        lastName,
        userId: createUser.id,
      })
    }

    if ([UserType.FIELD_RELATION_OFFICER].includes(userType)) {
      await FieldRelationOfficer.create({
        firstName,
        lastName,
        userId: createUser.id,
      })
    }

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'CREATE ENTRY (USER)',
      model: 'User',
      data: JSON.stringify({ username, password: 'REDACTED', firstName, lastName, userType }),
    })

    return response.json({
      status: 1,
      message: 'User created successfully!',
    })
  }

  public async update({ auth, request, response }: HttpContextContract) {
    if (auth.user?.userType !== UserType.SUPERADMIN) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Unauthorized user!',
      })
    }

    const { userId, firstName, lastName, userType, password, username } = await request.validate(
      UpdateUserValidator
    )

    const findUser = await User.find(userId)

    if (!findUser) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User doesnt exist!',
      })
    }

    if (username) {
      const findUserByUsername = await User.findBy('username', username)

      if (findUserByUsername && findUserByUsername.id !== findUser.id) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Username already exists!',
        })
      }

      findUser.username = username
    }

    if (userType) {
      if (findUser.userType === UserType.SUPERADMIN && userType !== UserType.SUPERADMIN) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Super admin cannot be modify!',
        })
      }

      findUser.userType = userType
    }

    if (password) {
      findUser.password = password
    }

    if ([UserType.ENCODER_1, UserType.ENCODER_2].includes(findUser.userType)) {
      const findEncoder = await Encoder.firstOrCreate(
        {
          userId: findUser.id,
        },
        {
          userId: findUser.id,
          firstName,
          lastName,
        }
      )

      if (!findEncoder) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Encoder doesnt exist!',
        })
      }

      if (firstName) {
        findEncoder.firstName = firstName
      }

      if (lastName) {
        findEncoder.lastName = lastName
      }

      await findEncoder.save()
    }

    if ([UserType.OPERATION_1, UserType.OPERATION_2].includes(findUser.userType)) {
      const findOperation = await Operation.firstOrCreate(
        {
          userId: findUser.id,
        },
        {
          userId: findUser.id,
          firstName,
          lastName,
        }
      )

      if (!findOperation) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Operation doesnt exist!',
        })
      }

      if (firstName) {
        findOperation.firstName = firstName
      }

      if (lastName) {
        findOperation.lastName = lastName
      }

      await findOperation.save()
    }

    if ([UserType.SALE_1, UserType.SALE_2].includes(findUser.userType)) {
      const findSale = await Sale.firstOrCreate(
        {
          userId: findUser.id,
        },
        {
          userId: findUser.id,
          firstName,
          lastName,
        }
      )

      if (!findSale) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Sale doesnt exist!',
        })
      }

      if (firstName) {
        findSale.firstName = firstName
      }

      if (lastName) {
        findSale.lastName = lastName
      }

      await findSale.save()
    }

    if ([UserType.FINANCE_1, UserType.FINANCE_2].includes(findUser.userType)) {
      const findFinance = await Finance.firstOrCreate(
        {
          userId: findUser.id,
        },
        {
          userId: findUser.id,
          firstName,
          lastName,
        }
      )

      if (!findFinance) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Finance doesnt exist!',
        })
      }

      if (firstName) {
        findFinance.firstName = firstName
      }

      if (lastName) {
        findFinance.lastName = lastName
      }

      await findFinance.save()
    }

    if ([UserType.ADMIN, UserType.SUPERADMIN].includes(findUser.userType)) {
      const findAdmin = await Admin.firstOrCreate(
        {
          userId: findUser.id,
        },
        {
          userId: findUser.id,
          firstName,
          lastName,
        }
      )

      if (!findAdmin) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Admin doesnt exist!',
        })
      }

      if (firstName) {
        findAdmin.firstName = firstName
      }

      if (lastName) {
        findAdmin.lastName = lastName
      }

      await findAdmin.save()
    }

    if ([UserType.AGRONOMIST, UserType.HEAD_AGRONOMIST].includes(findUser.userType)) {
      const findAgronomist = await Agronomist.firstOrCreate(
        {
          userId: findUser.id,
        },
        {
          userId: findUser.id,
          firstName,
          lastName,
        }
      )

      if (!findAgronomist) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Agronomist doesnt exist!',
        })
      }

      if (firstName) {
        findAgronomist.firstName = firstName
      }

      if (lastName) {
        findAgronomist.lastName = lastName
      }

      await findAgronomist.save()
    }

    if ([UserType.FIELD_RELATION_OFFICER].includes(findUser.userType)) {
      const findFieldRelationOfficer = await FieldRelationOfficer.firstOrCreate(
        {
          userId: findUser.id,
        },
        {
          userId: findUser.id,
          firstName,
          lastName,
        }
      )

      if (!findFieldRelationOfficer) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'FieldRelationOfficer doesnt exist!',
        })
      }

      if (firstName) {
        findFieldRelationOfficer.firstName = firstName
      }

      if (lastName) {
        findFieldRelationOfficer.lastName = lastName
      }

      await findFieldRelationOfficer.save()
    }

    await findUser.save()

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE ENTRY (USER)',
      model: 'User',
      data: JSON.stringify({ userId, firstName, lastName, userType, password: 'REDACTED' }),
    })

    return response.json({
      status: 1,
      message: 'User updated successfully!',
    })
  }

  public async viewAllByAdmin({ request, response }: HttpContextContract) {
    const { status, search } = request.qs()

    let findUsers = null as any

    findUsers = User.query()
      .preload('finance')
      .preload('operation')
      .preload('sale')
      .preload('encoder')
      .preload('admin')
      .preload('agronomist')
      .preload('fieldRelationOfficer')
      .whereNotIn('user_type', [
        UserType.FARMER,
        UserType.NONFARMER,
        UserType.TRADING_APP_STAFF,
        UserType.DEMAND,
      ])

    if (status) {
      findUsers = findUsers.whereIn('status', status)
    }

    if (search) {
      findUsers = findUsers.where((subQuery) => {
        subQuery
          .whereILike('email', `%${search}%`)
          .orWhereHas('admin', (subQuery) => {
            subQuery.where((subQuery) => {
              subQuery
                .whereILike('firstName', `%${search}%`)
                .orWhereILike('lastName', `%${search}%`)
            })
          })
          .orWhereHas('encoder', (subQuery) => {
            subQuery.where((subQuery) => {
              subQuery
                .whereILike('firstName', `%${search}%`)
                .orWhereILike('lastName', `%${search}%`)
            })
          })
      })
    }

    findUsers = await findUsers.orderBy('id', 'desc')

    return response.json({
      status: 1,
      data: findUsers,
    })
  }

  public async viewAllFarmersByAdmin({ request, response }: HttpContextContract) {
    const { status, search } = request.qs()

    let findUsers = null as any

    findUsers = User.query()
      .preload('farmer', (subQuery) => {
        subQuery
          .preload('farmerInfo')
          .preload('cropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('subCropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('familyProfiles')
          .preload('farmerInsurance')
          .preload('farmerDataPrivacy')
          .preload('farmerCreditScore')
          .preload('farmerVehicles')
          .preload('governmentIdentifications')
          .preload('realProperties')
          .preload('farmerBankDetails')
          .preload('farmerCharacterReferences')
          .preload('farmerVouchLeaders')
          .preload('farmerVouchMaos')
          .preload('farmerReferrers')
          .preload('farmerUtilities')
          .preload('farmerMobileDevice')
          .preload('chemicals', (subQuery) => {
            subQuery.preload('chemical', (subQuery) => {
              subQuery
                .preload('chemicalSubcategory')
                .preload('chemicalModeOfAction')
                .preload('chemicalActiveIngredients', (subQuery) => {
                  subQuery.preload('chemicalActiveIngredient')
                })
            })
          })
          .preload('seeds', (subQuery) => {
            subQuery.preload('seed')
          })
          .preload('seedSubcategories', (subQuery) => {
            subQuery.preload('seedSubcategory')
          })
          .preload('fertilizers', (subQuery) => {
            subQuery.preload('fertilizer')
          })
          .preload('farmerLandbankRequirements', (subQuery) => {
            subQuery.preload('processedBy')
          })
          .preload('farmerLoanRequirements')
      })
      .preload('wallet')
      .whereIn('user_type', [UserType.FARMER])

    if (status) {
      findUsers = findUsers.whereIn('status', status)
    }

    if (search) {
      findUsers = findUsers.where((subQuery) => {
        subQuery.whereILike('email', `%${search}%`).orWhereHas('farmer', (subQuery) => {
          subQuery.where((subQuery) => {
            subQuery.whereILike('firstName', `%${search}%`).orWhereILike('lastName', `%${search}%`)
          })
        })
      })
    }

    findUsers = await findUsers.orderBy('id', 'desc')

    return response.json({
      status: 1,
      data: findUsers,
    })
  }

  public async viewAllFarmersByAdminPaginated({ request, response }: HttpContextContract) {
    const { status, search, page = 1, pageSize = 10 } = request.qs()

    let findUsers = null as any

    findUsers = User.query()
      .preload('farmer', (subQuery) => {
        subQuery
          .preload('farmerInfo')
          .preload('cropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('subCropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('familyProfiles')
          .preload('farmerInsurance')
          .preload('farmerDataPrivacy')
          .preload('farmerCreditScore')
          .preload('farmerVehicles')
          .preload('governmentIdentifications')
          .preload('realProperties')
          .preload('farmerBankDetails')
          .preload('farmerCharacterReferences')
          .preload('farmerVouchLeaders')
          .preload('farmerVouchMaos')
          .preload('farmerReferrers')
          .preload('farmerUtilities')
          .preload('farmerMobileDevice')
          .preload('chemicals', (subQuery) => {
            subQuery.preload('chemical', (subQuery) => {
              subQuery
                .preload('chemicalSubcategory')
                .preload('chemicalModeOfAction')
                .preload('chemicalActiveIngredients', (subQuery) => {
                  subQuery.preload('chemicalActiveIngredient')
                })
            })
          })
          .preload('seeds', (subQuery) => {
            subQuery.preload('seed')
          })
          .preload('seedSubcategories', (subQuery) => {
            subQuery.preload('seedSubcategory')
          })
          .preload('fertilizers', (subQuery) => {
            subQuery.preload('fertilizer')
          })
          .preload('farmerLandbankRequirements', (subQuery) => {
            subQuery.preload('processedBy')
          })
          .preload('farmerLoanRequirements')
      })
      .preload('wallet')
      .whereIn('user_type', [UserType.FARMER])

    if (status) {
      findUsers = findUsers.whereIn('status', status)
    }

    if (search) {
      findUsers = findUsers.where((subQuery) => {
        subQuery.whereILike('email', `%${search}%`).orWhereHas('farmer', (subQuery) => {
          subQuery.where((subQuery) => {
            subQuery.whereILike('firstName', `%${search}%`).orWhereILike('lastName', `%${search}%`)
          })
        })
      })
    }

    findUsers = await findUsers.orderBy('id', 'desc').paginate(page, pageSize)

    return response.json({
      status: 1,
      data: findUsers,
    })
  }

  public async viewByIdByAdmin({ request, response }: HttpContextContract) {
    const { userId } = request.params()

    const findUser = await User.query()
      .preload('farmer', (subQuery) => {
        subQuery
          .preload('farmerInfo')
          .preload('cropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('subCropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('familyProfiles')
          .preload('farmerInsurance')
          .preload('farmerDataPrivacy')
          .preload('farmerCreditScore')
          .preload('farmerVehicles')
          .preload('governmentIdentifications')
          .preload('realProperties')
          .preload('farmerBankDetails')
          .preload('farmerCharacterReferences')
          .preload('farmerVouchLeaders')
          .preload('farmerVouchMaos')
          .preload('farmerReferrers')
          .preload('farmerUtilities')
          .preload('farmerMobileDevice')
          .preload('chemicals', (subQuery) => {
            subQuery.preload('chemical', (subQuery) => {
              subQuery
                .preload('chemicalSubcategory')
                .preload('chemicalModeOfAction')
                .preload('chemicalActiveIngredients', (subQuery) => {
                  subQuery.preload('chemicalActiveIngredient')
                })
            })
          })
          .preload('seeds', (subQuery) => {
            subQuery.preload('seed')
          })
          .preload('seedSubcategories', (subQuery) => {
            subQuery.preload('seedSubcategory')
          })
          .preload('fertilizers', (subQuery) => {
            subQuery.preload('fertilizer')
          })
          .preload('farmerLandbankRequirements', (subQuery) => {
            subQuery.preload('processedBy')
          })
          .preload('farmerLoanRequirements')
      })
      .preload('encoder')
      .preload('finance')
      .preload('operation')
      .preload('sale')
      .preload('admin')
      .preload('agronomist')
      .preload('fieldRelationOfficer')
      .preload('wallet')
      .where('id', userId)
      .first()

    return response.json({
      status: 1,
      data: findUser,
    })
  }

  public async activate({ auth, request, response }: HttpContextContract) {
    const { userId } = request.body()

    const checkUser = await User.find(userId)

    if (!checkUser) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User doesnt exist!',
      })
    }

    checkUser.status = UserStatusType.ACTIVATED

    await checkUser.save()

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE ENTRY (ACTIVATE USER)',
      model: 'User',
      dataJson: { userId },
    })

    return response.json({
      status: 1,
      message: 'User activated successfully!',
    })
  }

  public async deactivate({ auth, request, response }: HttpContextContract) {
    const { userId } = request.body()

    const checkUser = await User.find(userId)

    if (!checkUser) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User doesnt exist!',
      })
    }

    if (checkUser.userType === UserType.SUPERADMIN) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Super admin cannot be modify!',
      })
    }

    checkUser.status = UserStatusType.PENDING_FROM_ADMIN

    await checkUser.save()

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE ENTRY (DEACTIVATE USER)',
      model: 'User',
      dataJson: { userId },
    })

    return response.json({
      status: 1,
      message: 'User deactivated successfully!',
    })
  }

  public async updateFarmer({ auth, request, response }: HttpContextContract) {
    const {
      userId,
      password,
      userImage,
      firstName,
      lastName,
      middleName,
      birthDate,
      placeOfBirth,
      religion,
      gender,
      civilStatus,
      height,
      weight,
      mobileNumber,
      addressHouseNumber,
      addressProvince,
      addressStreet,
      addressCity,
      addressBarangay,
      addressZipCode,
      educationalAttainment,
      educationalIsGraduate,
      educationalDegree,
      occupationTitle,
      occupation,
      occupationStatus,
      occupationEmployerName,
      occupationEmployerAddress,
      occupationBusinessName,
      occupationBusinessAddress,
      occupationBusinessContact,
      occupationAnnualIncome,
      skillsFarming,
      skillsFishing,
      skillsLivestock,
      skillsConstruction,
      skillsProcessing,
      skillsServicing,
      skillsCraft,
      skillsOthers,
      governmentIdentification,
      familyProfile,
      realProperty,
      bankDetail,
      characterReference,
      vehicleOwned,
      farmAddress,
      farmArea,
      farmOwnership,
      otherFarmOwnership,
      farmerVehicle,
      cropsPlanted,
      subCropsPlanted,
      seed,
      seedSubcategory,
      fertilizer,
      chemical,
      landCategory,
      crop,
      phase,
      ownerCultivator,
      tenant,
      cltEp,
      lessee,
      naturalDisasterCover,
      multiRiskCover,
      desiredAmountCover,
      additionalAmountCover,
      transplantingDate,
      harvestDate,
      sowingDate,
      seedbedding,
      planting,
      plantCare,
      insurancePremium,
      insuranceLocationPlan,
      nationality,
      yearResiding,
      residenceOwnership,
      otherMobileNumber,
      vouchByLeader,
      vouchByMao,
      referrer,
      biometric,
      hasBiometric,
      mobileDeviceBrand,
      mobileDeviceModel,
      farmerUtilities,
      permanentAddressHouseNumber,
      permanentAddressStreet,
      permanentAddressProvince,
      permanentAddressCity,
      permanentAddressBarangay,
      permanentAddressZipCode,
      permanentAddressLengthOfStay,
      addressLengthOfStay,
      sourceOfFunds,
      landbankAccounts,
      priceBasedBy,
      facialRecognition,
      purchaserSellingLocation,
      purchaserFullname,
      purchaserContactNumber,
      farmAddressHouseNumber,
      farmAddressStreet,
      farmAddressProvince,
      farmAddressCity,
      farmAddressBarangay,
      farmAddressZipCode,
      farmAddressCountry,
      telephoneNumber,
      mothersMaidenName,
      facebookName,
      email,
      spouseName,
      spouseMobileNumber,
      waterSource,
      fertilizerUsed,
      pesticideUsed,
      farmImplements,
      averageYieldPerYear,
      monthlyGrossIncome,
      isMemberOfOrganization,
      organizationName,
      organizationPosition,
      hasPastFarmLoans,
      pastFarmLoans,
      hasPastFarmLoanPaid,
      hasNeedFarmLoan,
      needFarmLoanReason,
      isInterestedToSellAtTradingPost,
    } = await request.validate(UpdateFarmerValidator)

    if (civilStatus === FarmerCivilStatus.MARRIED) {
      if (!spouseName) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Spouse name is required',
        })
      }
    }

    const findUser = await User.find(userId)

    if (!findUser) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User doesnt exist!',
      })
    }

    if (email) {
      const findUserByEmail = await User.findBy('email', email)

      if (findUserByEmail && findUserByEmail.id !== findUser.id) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Email already exists!',
        })
      }
    }

    if (userImage) {
      await userImage.moveToDisk('./users/profile')

      findUser.userImg = userImage.fileName
    }

    if (insuranceLocationPlan) {
      await insuranceLocationPlan.moveToDisk('./users/insurance')
    }

    if (biometric) {
      await biometric.moveToDisk('./users/biometric')
    }

    if (facialRecognition) {
      await facialRecognition.moveToDisk('./users/facial_recognition')
    }

    if (password) {
      findUser.password = password
    }

    const checkFarmer = await Farmer.firstOrCreate(
      {
        userId: findUser.id,
      },
      {
        userId: findUser.id,
        mobileNumber,
        firstName,
        lastName,
        middleName,
        birthDate,
        placeOfBirth,
        religion,
        gender,
        civilStatus,
        height,
        weight,
        address: JSON.stringify({
          addressHouseNumber,
          addressStreet,
          addressProvince,
          addressCity,
          addressBarangay,
          addressZipCode,
        }),
        addressHouseNumber,
        addressStreet,
        addressProvince,
        addressCity,
        addressBarangay,
        addressZipCode,
        educationalAttainment,
        educationalIsGraduate: educationalIsGraduate || 0,
        educationalDegree,
        occupationTitle,
        occupation,
        occupationStatus,
        occupationEmployerName,
        occupationEmployerAddress,
        occupationBusinessName,
        occupationBusinessAddress,
        occupationBusinessContact,
        occupationAnnualIncome,
        skillsFarming,
        skillsFishing,
        skillsLivestock,
        skillsConstruction,
        skillsProcessing,
        skillsServicing,
        skillsCraft,
        skillsOthers,
        vehicleOwned,
        biometric: biometric?.fileName,
        hasBiometric: biometric ? 1 : 0,
        facialRecognition: facialRecognition?.fileName,
        hasFacialRecognition: facialRecognition ? 1 : 0,
        permanentAddress: JSON.stringify({
          permanentAddressHouseNumber,
          permanentAddressStreet,
          permanentAddressProvince,
          permanentAddressCity,
          permanentAddressBarangay,
          permanentAddressZipCode,
        }),
        permanentAddressHouseNumber,
        permanentAddressStreet,
        permanentAddressProvince,
        permanentAddressCity,
        permanentAddressBarangay,
        permanentAddressZipCode,
        permanentAddressLengthOfStay,
        addressLengthOfStay,
        sourceOfFunds,
        landbankAccounts,
        telephoneNumber,
        mothersMaidenName,
        facebookName,
      }
    )

    if (checkFarmer.$isLocal) {
      await FarmerInfo.create({
        farmerId: checkFarmer.id,
        farmAddress,
        farmAddressHouseNumber,
        farmAddressStreet,
        farmAddressProvince,
        farmAddressCity,
        farmAddressBarangay,
        farmAddressZipCode,
        farmAddressCountry,
        farmOwnership: farmOwnership as FarmOwnership,
        otherFarmOwnership,
        waterSource: waterSource,
        fertilizerUsed: fertilizerUsed as EFertilizerUsed,
        pesticideUsed: pesticideUsed as EPesticideUsed,
        farmImplements: farmImplements,
        averageYieldPerYear: averageYieldPerYear,
        farmArea,
        nationality,
        yearResiding,
        residenceOwnership,
        otherMobileNumber,
        priceBasedBy,
        purchaserSellingLocation,
        purchaserFullname,
        purchaserContactNumber,
        monthlyGrossIncome,
        isMemberOfOrganization,
        organizationName,
        organizationPosition,
        hasPastFarmLoans,
        pastFarmLoans,
        hasPastFarmLoanPaid,
        hasNeedFarmLoan,
        needFarmLoanReason,
        isInterestedToSellAtTradingPost,
      })

      await FarmerInsurance.create({
        farmerId: checkFarmer.id,
        landCategory,
        crop,
        phase,
        ownerCultivator,
        tenant,
        cltEp,
        lessee,
        naturalDisasterCover,
        multiRiskCover,
        desiredAmountCover,
        additionalAmountCover,
        transplantingDate,
        harvestDate,
        sowingDate,
        seedbedding: { data: seedbedding },
        planting: { data: planting },
        plantCare: { data: plantCare },
        insurancePremium,
        insuranceLocationPlan: insuranceLocationPlan?.fileName,
      })

      if (cropsPlanted && cropsPlanted.length > 0) {
        const mappedCropsPlanted = cropsPlanted?.map((cropId) => {
          return {
            farmerId: checkFarmer.id,
            cropId: cropId,
          }
        })

        await FarmerCropPivot.createMany(mappedCropsPlanted)
      }

      if (subCropsPlanted && subCropsPlanted.length > 0) {
        const mappedSubCropsPlanted = subCropsPlanted?.map((cropId) => {
          return {
            farmerId: checkFarmer.id,
            cropId: cropId,
          }
        })

        await FarmerSubCropPivot.createMany(mappedSubCropsPlanted)
      }

      if (fertilizer && fertilizer.length > 0) {
        const mappedFertilizer = fertilizer?.map((fertilizerId) => {
          return {
            farmerId: checkFarmer.id,
            fertilizerId: fertilizerId,
          }
        })

        await FarmerFertilizerPivot.createMany(mappedFertilizer)
      }

      if (seed && seed.length > 0) {
        const mappedSeed = seed?.map((seedId) => {
          return {
            farmerId: checkFarmer.id,
            seedId: seedId,
          }
        })

        await FarmerSeedPivot.createMany(mappedSeed)
      }

      if (seedSubcategory && seedSubcategory.length > 0) {
        const mappedSeedSubcategory = seedSubcategory?.map((seedSubcategoryId) => {
          return {
            farmerId: checkFarmer.id,
            seedSubcategoryId: seedSubcategoryId,
          }
        })

        await FarmerSeedSubcategoryPivot.createMany(mappedSeedSubcategory)
      }

      if (chemical && chemical.length > 0) {
        const mappedChemical = chemical?.map((chemicalId) => {
          return {
            farmerId: checkFarmer.id,
            chemicalId: chemicalId,
          }
        })

        await FarmerChemicalPivot.createMany(mappedChemical)
      }

      if (familyProfile && familyProfile.length > 0) {
        const mappedFamilyProfile = familyProfile?.map((familyProfileItem) => {
          return {
            ...familyProfileItem,
            farmerId: checkFarmer.id,
            isBarbazaMember: familyProfileItem.isBarbazaMember,
            isBeneficiaries: familyProfileItem.isBeneficiaries,
            identifier: `${checkFarmer.id}-${familyProfileItem.name}`,
          }
        })

        await FarmerFamilyProfile.createMany(mappedFamilyProfile)
      }

      if (realProperty && realProperty.length > 0) {
        const mappedRealProperty = realProperty?.map((realPropertyItem) => {
          return {
            ...realPropertyItem,
            farmerId: checkFarmer.id,
            identifier: `${checkFarmer.id}-${realPropertyItem.propertyTitleNumber}`,
          }
        })

        await FarmerRealProperty.createMany(mappedRealProperty)
      }

      if (bankDetail && bankDetail.length > 0) {
        const mappedBankingDetail = bankDetail?.map((bankDetailItem) => {
          return {
            ...bankDetailItem,
            farmerId: checkFarmer.id,
            identifier: `${checkFarmer.id}-${bankDetailItem.bankAccountNumber}`,
          }
        })

        await FarmerBankingDetail.createMany(mappedBankingDetail)
      }

      if (characterReference && characterReference.length > 0) {
        const mappedCharacterReference = characterReference?.map((characterReferenceItem) => {
          return {
            ...characterReferenceItem,
            farmerId: checkFarmer.id,
            identifier: `${checkFarmer.id}-${characterReferenceItem.name}`,
          }
        })

        await FarmerCharacterReference.createMany(mappedCharacterReference)
      }

      if (farmerVehicle && farmerVehicle.length > 0) {
        const mappedFarmerVehicle = await Promise.all(
          farmerVehicle?.map(async (farmerVehicleItem) => {
            if (farmerVehicleItem && farmerVehicleItem.vehiclePlateNumber) {
              const entryAttachment = request.file(
                `farmerVehicle_vehicleOrcr_${farmerVehicleItem.vehiclePlateNumber}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/vehicleorcr')

              return {
                ...farmerVehicleItem,
                farmerId: checkFarmer.id,
                vehicleOrcr: entryAttachment?.fileName ?? undefined,
                identifier: `${checkFarmer.id}-${farmerVehicleItem.vehiclePlateNumber}`,
              }
            }

            return {
              ...farmerVehicleItem,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${farmerVehicleItem.vehiclePlateNumber}`,
            }
          })
        )

        await FarmerVehicle.updateOrCreateMany('identifier', mappedFarmerVehicle)

        findUser.isSync = UserSyncStatus.NOT_SYNCED
      }

      if (governmentIdentification && governmentIdentification.length > 0) {
        const mappedGovernmentIdentification = await Promise.all(
          governmentIdentification?.map(async (governmentIdentificationItem) => {
            if (governmentIdentificationItem && governmentIdentificationItem.governmentIdNumber) {
              const entryAttachment = request.file(
                `governmentIdentification_${governmentIdentificationItem.governmentIdNumber}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              if (entryAttachment) {
                await entryAttachment?.moveToDisk('./users/governmentid')

                return {
                  ...governmentIdentificationItem,
                  farmerId: checkFarmer.id,
                  governmentIdImage: entryAttachment?.fileName ?? undefined,
                  identifier: `${checkFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
                }
              }

              return {
                ...governmentIdentificationItem,
                farmerId: checkFarmer.id,
                identifier: `${checkFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
              }
            }

            return {
              ...governmentIdentificationItem,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
            }
          })
        )

        await FarmerGovernmentIdentification.createMany(mappedGovernmentIdentification)
      }

      if (vouchByLeader && vouchByLeader.length > 0) {
        const mappedVouchByLeader = await Promise.all(
          vouchByLeader?.map(async (vouchByLeaderItem) => {
            if (vouchByLeaderItem && vouchByLeaderItem.identifier) {
              const entryAttachment = request.file(
                `vouchByLeader_${vouchByLeaderItem.identifier}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/vouchbyleader')

              return {
                ...vouchByLeaderItem,
                farmerId: checkFarmer.id,
                vouchByLeadersAttachment: entryAttachment?.fileName ?? undefined,
              }
            }

            return {
              ...vouchByLeaderItem,
              farmerId: checkFarmer.id,
            }
          })
        )

        await FarmerVouchLeader.createMany(mappedVouchByLeader)
      }

      if (vouchByMao && vouchByMao.length > 0) {
        const mappedVouchByMao = await Promise.all(
          vouchByMao?.map(async (vouchByMaoItem) => {
            if (vouchByMaoItem && vouchByMaoItem.identifier) {
              const entryAttachment = request.file(`vouchByMao_${vouchByMaoItem.identifier}`, {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              })

              await entryAttachment?.moveToDisk('./users/vouchbymao')

              return {
                ...vouchByMaoItem,
                farmerId: checkFarmer.id,
                vouchByMaosAttachment: entryAttachment?.fileName ?? undefined,
              }
            }

            return {
              ...vouchByMaoItem,
              farmerId: checkFarmer.id,
            }
          })
        )

        await FarmerVouchMao.createMany(mappedVouchByMao)
      }

      if (referrer && referrer.length > 0) {
        const mappedReferrer = await Promise.all(
          referrer?.map(async (referrerItem) => {
            if (referrerItem && referrerItem.identifier) {
              const entryAttachment = request.file(`referrer_${referrerItem.identifier}`, {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              })

              await entryAttachment?.moveToDisk('./users/referrer')

              return {
                ...referrerItem,
                farmerId: checkFarmer.id,
                referrerAttachment: entryAttachment?.fileName ?? undefined,
              }
            }

            return {
              ...referrerItem,
              farmerId: checkFarmer.id,
            }
          })
        )

        await FarmerReferrer.createMany(mappedReferrer)
      }

      if (mobileDeviceBrand || mobileDeviceModel) {
        await FarmerMobileDevice.create({
          farmerId: checkFarmer.id,
          mobileDeviceBrand,
          mobileDeviceModel,
        })
      }

      if (farmerUtilities && farmerUtilities.length > 0) {
        const mappedFarmerUtilities = await Promise.all(
          farmerUtilities?.map(async (farmerUtilitiesItem) => {
            if (farmerUtilitiesItem && farmerUtilitiesItem.identifier) {
              const entryAttachment = request.file(
                `farmerUtilities_${farmerUtilitiesItem.identifier}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/utility')

              return {
                ...farmerUtilitiesItem,
                farmerId: checkFarmer.id,
                bill: entryAttachment?.fileName ?? undefined,
              }
            }

            return {
              ...farmerUtilitiesItem,
              farmerId: checkFarmer.id,
            }
          })
        )

        await FarmerUtility.createMany(mappedFarmerUtilities)
      }
    }

    if (!checkFarmer.$isLocal) {
      if (vehicleOwned !== undefined) {
        checkFarmer.vehicleOwned = vehicleOwned
      }

      if (skillsOthers !== undefined) {
        checkFarmer.skillsOthers = skillsOthers
      }

      if (skillsCraft !== undefined) {
        checkFarmer.skillsCraft = skillsCraft
      }

      if (skillsServicing !== undefined) {
        checkFarmer.skillsServicing = skillsServicing
      }

      if (skillsProcessing !== undefined) {
        checkFarmer.skillsProcessing = skillsProcessing
      }

      if (skillsConstruction !== undefined) {
        checkFarmer.skillsConstruction = skillsConstruction
      }

      if (skillsLivestock !== undefined) {
        checkFarmer.skillsLivestock = skillsLivestock
      }

      if (skillsFishing !== undefined || skillsFishing === 0) {
        checkFarmer.skillsFishing = skillsFishing || 0
      }

      if (skillsFarming !== undefined) {
        checkFarmer.skillsFarming = skillsFarming
      }

      if (sourceOfFunds !== undefined) {
        checkFarmer.sourceOfFunds = sourceOfFunds
      }

      if (landbankAccounts !== undefined) {
        checkFarmer.landbankAccounts = landbankAccounts
      }

      if (occupation || occupationAnnualIncome !== undefined || occupationAnnualIncome === 0) {
        checkFarmer.occupationAnnualIncome = occupationAnnualIncome || 0
      }

      if (occupation || occupationBusinessContact !== undefined) {
        checkFarmer.occupationBusinessContact = occupationBusinessContact
      }

      if (occupation || occupationBusinessAddress !== undefined) {
        checkFarmer.occupationBusinessAddress = occupationBusinessAddress
      }

      if (occupation || occupationBusinessContact !== undefined) {
        checkFarmer.occupationBusinessContact = occupationBusinessContact
      }

      if (occupation || occupationBusinessName !== undefined) {
        checkFarmer.occupationBusinessName = occupationBusinessName
      }

      if (occupation || occupationEmployerAddress !== undefined) {
        checkFarmer.occupationEmployerAddress = occupationEmployerAddress
      }

      if (occupation || occupationEmployerName !== undefined) {
        checkFarmer.occupationEmployerName = occupationEmployerName
      }

      if (occupation || occupationStatus !== undefined) {
        checkFarmer.occupationStatus = occupationStatus
      }

      if (occupation !== undefined) {
        checkFarmer.occupation = occupation
      }

      if (occupation || occupationTitle !== undefined) {
        checkFarmer.occupationTitle = occupationTitle
      }

      if (educationalAttainment || educationalDegree !== undefined) {
        checkFarmer.educationalDegree = educationalDegree
      }

      if (
        educationalAttainment ||
        educationalIsGraduate !== undefined ||
        educationalIsGraduate === 0
      ) {
        checkFarmer.educationalIsGraduate = educationalIsGraduate || 0
      }

      if (educationalAttainment !== undefined) {
        checkFarmer.educationalAttainment = educationalAttainment
      }

      if (addressZipCode !== undefined) {
        checkFarmer.addressZipCode = addressZipCode
      }

      if (addressCity !== undefined) {
        checkFarmer.addressCity = addressCity
      }

      if (addressBarangay !== undefined) {
        checkFarmer.addressBarangay = addressBarangay
      }

      if (addressProvince !== undefined) {
        checkFarmer.addressProvince = addressProvince
      }

      if (addressHouseNumber !== undefined) {
        checkFarmer.addressHouseNumber = addressHouseNumber
      }

      if (addressStreet !== undefined) {
        checkFarmer.addressStreet = addressStreet
      }

      checkFarmer.address = JSON.stringify({
        addressHouseNumber: checkFarmer.addressHouseNumber,
        addressStreet: checkFarmer.addressStreet,
        addressProvince: checkFarmer.addressProvince,
        addressCity: checkFarmer.addressCity,
        addressBarangay: checkFarmer.addressBarangay,
        addressZipCode: checkFarmer.addressZipCode,
      })

      if (addressLengthOfStay !== undefined || addressLengthOfStay === 0) {
        checkFarmer.addressLengthOfStay = addressLengthOfStay || 0
      }

      if (permanentAddressZipCode !== undefined) {
        checkFarmer.permanentAddressZipCode = permanentAddressZipCode
      }

      if (permanentAddressCity !== undefined) {
        checkFarmer.permanentAddressCity = permanentAddressCity
      }

      if (permanentAddressBarangay !== undefined) {
        checkFarmer.permanentAddressBarangay = permanentAddressBarangay
      }

      if (permanentAddressProvince !== undefined) {
        checkFarmer.permanentAddressProvince = permanentAddressProvince
      }

      if (permanentAddressHouseNumber !== undefined) {
        checkFarmer.permanentAddressHouseNumber = permanentAddressHouseNumber
      }

      if (permanentAddressStreet !== undefined) {
        checkFarmer.permanentAddressStreet = permanentAddressStreet
      }

      checkFarmer.permanentAddress = JSON.stringify({
        permanentAddressHouseNumber: checkFarmer.permanentAddressHouseNumber,
        permanentAddressStreet: checkFarmer.permanentAddressStreet,
        permanentAddressProvince: checkFarmer.permanentAddressProvince,
        permanentAddressCity: checkFarmer.permanentAddressCity,
        permanentAddressBarangay: checkFarmer.permanentAddressBarangay,
        permanentAddressZipCode: checkFarmer.permanentAddressZipCode,
      })

      if (permanentAddressLengthOfStay !== undefined || permanentAddressLengthOfStay === 0) {
        checkFarmer.permanentAddressLengthOfStay = permanentAddressLengthOfStay || 0
      }

      if (weight !== undefined) {
        checkFarmer.weight = weight || 0
      }

      if (height !== undefined) {
        checkFarmer.height = height || 0
      }

      if (civilStatus !== undefined) {
        checkFarmer.civilStatus = civilStatus
      }

      if (spouseName && checkFarmer.civilStatus === FarmerCivilStatus.MARRIED) {
        checkFarmer.spouseName = spouseName
      } else if (checkFarmer.civilStatus !== FarmerCivilStatus.MARRIED) {
        checkFarmer.spouseName = null
      }

      if (spouseMobileNumber && checkFarmer.civilStatus === FarmerCivilStatus.MARRIED) {
        checkFarmer.spouseMobileNumber = spouseMobileNumber
      } else if (checkFarmer.civilStatus !== FarmerCivilStatus.MARRIED) {
        checkFarmer.spouseMobileNumber = null
      }

      if (firstName !== undefined) {
        checkFarmer.firstName = firstName
      }

      if (middleName !== undefined) {
        checkFarmer.middleName = middleName
      }

      if (lastName !== undefined) {
        checkFarmer.lastName = lastName
      }

      if (birthDate !== undefined) {
        checkFarmer.birthDate = birthDate
      }

      if (placeOfBirth !== undefined) {
        checkFarmer.placeOfBirth = placeOfBirth
      }

      if (religion !== undefined) {
        checkFarmer.religion = religion
      }

      if (gender !== undefined) {
        checkFarmer.gender = gender
      }

      if (mobileNumber !== undefined) {
        checkFarmer.mobileNumber = mobileNumber
      }

      if (hasBiometric !== undefined || hasBiometric === 0) {
        checkFarmer.hasBiometric = hasBiometric || 0
      }

      if (biometric && biometric.fileName) {
        checkFarmer.biometric = biometric?.fileName
        checkFarmer.hasBiometric = 1
      }

      if (facialRecognition && facialRecognition.fileName) {
        checkFarmer.facialRecognition = facialRecognition?.fileName
        checkFarmer.hasFacialRecognition = 1
      }

      if (telephoneNumber !== undefined) {
        checkFarmer.telephoneNumber = telephoneNumber
      }

      if (mothersMaidenName !== undefined) {
        checkFarmer.mothersMaidenName = mothersMaidenName
      }

      if (facebookName !== undefined) {
        checkFarmer.facebookName = facebookName
      }

      if (cropsPlanted) {
        if (cropsPlanted.length === 0) {
          await FarmerCropPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checkCropsPlanted = await FarmerCropPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('cropId', cropsPlanted)

          const checkMappedcropsPlanted = checkCropsPlanted.map((item) => item.cropId)

          const mappedCropsPlanted = cropsPlanted
            .filter((cropId) => !checkMappedcropsPlanted.includes(cropId))
            .map((cropId) => {
              return {
                farmerId: checkFarmer.id,
                cropId: cropId,
              }
            })

          await FarmerCropPivot.createMany(mappedCropsPlanted)

          await FarmerCropPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('cropId', cropsPlanted)
            .delete()
        }
      }

      if (subCropsPlanted) {
        if (subCropsPlanted.length === 0) {
          await FarmerSubCropPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checksubCropsPlanted = await FarmerSubCropPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('cropId', subCropsPlanted)

          const checkMappedSubCropsPlanted = checksubCropsPlanted.map((item) => item.cropId)

          const mappedSubCropsPlanted = subCropsPlanted
            .filter((cropId) => !checkMappedSubCropsPlanted.includes(cropId))
            .map((cropId) => {
              return {
                farmerId: checkFarmer.id,
                cropId: cropId,
              }
            })

          await FarmerSubCropPivot.createMany(mappedSubCropsPlanted)

          await FarmerSubCropPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('cropId', subCropsPlanted)
            .delete()
        }
      }

      if (fertilizer) {
        if (fertilizer.length === 0) {
          await FarmerFertilizerPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checkfertilizer = await FarmerFertilizerPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('fertilizerId', fertilizer)

          const checkMappedfertilizer = checkfertilizer.map((item) => item.fertilizerId)

          const mappedfertilizer = fertilizer
            .filter((fertilizerId) => !checkMappedfertilizer.includes(fertilizerId))
            .map((fertilizerId) => {
              return {
                farmerId: checkFarmer.id,
                fertilizerId: fertilizerId,
              }
            })

          await FarmerFertilizerPivot.createMany(mappedfertilizer)

          await FarmerFertilizerPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('fertilizerId', fertilizer)
            .delete()
        }
      }

      if (seed) {
        if (seed.length === 0) {
          await FarmerSeedPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checkseed = await FarmerSeedPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('seedId', seed)

          const checkMappedseed = checkseed.map((item) => item.seedId)

          const mappedseed = seed
            .filter((seedId) => !checkMappedseed.includes(seedId))
            .map((seedId) => {
              return {
                farmerId: checkFarmer.id,
                seedId: seedId,
              }
            })

          await FarmerSeedPivot.createMany(mappedseed)

          await FarmerSeedPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('seedId', seed)
            .delete()
        }
      }

      if (seedSubcategory) {
        if (seedSubcategory.length === 0) {
          await FarmerSeedSubcategoryPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checkseedSubcategory = await FarmerSeedSubcategoryPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('seedSubcategoryId', seedSubcategory)

          const checkMappedseedSubcategory = checkseedSubcategory.map(
            (item) => item.seedSubcategoryId
          )

          const mappedseedSubcategory = seedSubcategory
            .filter((seedSubcategoryId) => !checkMappedseedSubcategory.includes(seedSubcategoryId))
            .map((seedSubcategoryId) => {
              return {
                farmerId: checkFarmer.id,
                seedSubcategoryId: seedSubcategoryId,
              }
            })

          await FarmerSeedSubcategoryPivot.createMany(mappedseedSubcategory)

          await FarmerSeedSubcategoryPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('seedSubcategoryId', seedSubcategory)
            .delete()
        }
      }

      if (chemical) {
        if (chemical.length === 0) {
          await FarmerChemicalPivot.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const checkchemical = await FarmerChemicalPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereIn('chemicalId', chemical)

          const checkMappedchemical = checkchemical.map((item) => item.chemicalId)

          const mappedchemical = chemical
            .filter((chemicalId) => !checkMappedchemical.includes(chemicalId))
            .map((chemicalId) => {
              return {
                farmerId: checkFarmer.id,
                chemicalId: chemicalId,
              }
            })

          await FarmerChemicalPivot.createMany(mappedchemical)

          await FarmerChemicalPivot.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('chemicalId', chemical)
            .delete()
        }
      }

      if (familyProfile) {
        if (familyProfile.length === 0) {
          await FarmerFamilyProfile.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const mappedFamilyProfile = familyProfile.map((item) => {
            return {
              ...item,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${item.name}`,
            }
          })

          const updateFamily = await FarmerFamilyProfile.updateOrCreateMany(
            'identifier',
            mappedFamilyProfile
          )

          const mappedUpdateFamilyId = updateFamily.map((item) => item.id)

          await FarmerFamilyProfile.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedUpdateFamilyId)
            .delete()
        }
      }

      if (realProperty) {
        if (realProperty.length === 0) {
          await FarmerRealProperty.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const mappedRealProperty = realProperty.map((item) => {
            return {
              ...item,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${item.propertyTitleNumber}`,
            }
          })

          const updateRealProperty = await FarmerRealProperty.updateOrCreateMany(
            'identifier',
            mappedRealProperty
          )

          const mappedRealPropertyId = updateRealProperty.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerRealProperty.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedRealPropertyId)
            .delete()
        }
      }

      if (bankDetail) {
        if (bankDetail.length === 0) {
          await FarmerBankingDetail.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const mappedBankingDetail = bankDetail.map((item) => {
            return {
              ...item,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${item.bankAccountNumber}`,
            }
          })

          const updateBankingDetail = await FarmerBankingDetail.updateOrCreateMany(
            'identifier',
            mappedBankingDetail
          )

          const mappedBankingDetailId = updateBankingDetail.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerBankingDetail.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedBankingDetailId)
            .delete()
        }
      }

      if (characterReference) {
        if (characterReference.length === 0) {
          await FarmerCharacterReference.query().where('farmerId', checkFarmer.id).delete()
        } else {
          const mappedCharacterReference = characterReference.map((item) => {
            return {
              ...item,
              farmerId: checkFarmer.id,
              identifier: `${checkFarmer.id}-${item.name}`,
            }
          })

          const updateCharacterReference = await FarmerCharacterReference.updateOrCreateMany(
            'identifier',
            mappedCharacterReference
          )

          const mappedCharacterReferenceId = updateCharacterReference.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerCharacterReference.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedCharacterReferenceId)
            .delete()
        }
      }

      if (farmerVehicle) {
        if (farmerVehicle.length === 0) {
          await FarmerVehicle.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpFarmerVehicle = [] as any

          // CHECK UPLOADED FILE
          for (const vehicleItem of farmerVehicle) {
            if (vehicleItem.vehiclePlateNumber) {
              const entryAttachment = request.file(
                `farmerVehicle_vehicleOrcr_${vehicleItem.vehiclePlateNumber}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/vehicleorcr')

              if (entryAttachment?.fileName) {
                tmpFarmerVehicle.push({
                  ...vehicleItem,
                  vehicleOrcr: entryAttachment?.fileName,
                  farmerId: checkFarmer.id,
                  identifier: `${checkFarmer.id}-${vehicleItem.vehiclePlateNumber}`,
                })
              } else {
                tmpFarmerVehicle.push({
                  ...vehicleItem,
                  farmerId: checkFarmer.id,
                  identifier: `${checkFarmer.id}-${vehicleItem.vehiclePlateNumber}`,
                })
              }
            }
          }

          const updateFarmerVehicle = await FarmerVehicle.updateOrCreateMany(
            'identifier',
            tmpFarmerVehicle
          )

          const mappedFarmerVehicleId = updateFarmerVehicle.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerVehicle.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedFarmerVehicleId)
            .delete()
        }

        findUser.isSync = UserSyncStatus.NOT_SYNCED
      }

      if (governmentIdentification) {
        if (governmentIdentification.length === 0) {
          await FarmerGovernmentIdentification.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpGovernmentIdentification = [] as any

          // CHECK UPLOADED FILE
          for (const governmentIdentificationItem of governmentIdentification) {
            if (governmentIdentificationItem.governmentIdNumber) {
              const entryAttachment = request.file(
                `governmentIdentification_${governmentIdentificationItem.governmentIdNumber}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              if (entryAttachment) {
                await entryAttachment?.moveToDisk('./users/governmentid')
                tmpGovernmentIdentification.push({
                  ...governmentIdentificationItem,
                  farmerId: checkFarmer.id,
                  governmentIdImage: entryAttachment?.fileName,
                  identifier: `${checkFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
                })
              } else {
                tmpGovernmentIdentification.push({
                  ...governmentIdentificationItem,
                  farmerId: checkFarmer.id,
                  identifier: `${checkFarmer.id}-${governmentIdentificationItem.governmentIdNumber}`,
                })
              }
            }
          }

          const updateGovernmentIdentification =
            await FarmerGovernmentIdentification.updateOrCreateMany(
              'identifier',
              tmpGovernmentIdentification
            )

          const mappedGovernmentIdentificationId = updateGovernmentIdentification.map(
            (item) => item.id
          )

          // DELETE NON EXISTING ENTRY
          await FarmerGovernmentIdentification.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedGovernmentIdentificationId)
            .delete()
        }
      }

      if (vouchByLeader) {
        if (vouchByLeader.length === 0) {
          await FarmerVouchLeader.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpVouchLeader = [] as any

          // CHECK UPLOADED FILE
          for (const vouchByLeaderItem of vouchByLeader) {
            if (vouchByLeaderItem.identifier) {
              const entryAttachment = request.file(
                `vouchByLeader_${vouchByLeaderItem.identifier}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/vouchbyleader')

              if (entryAttachment?.fileName) {
                tmpVouchLeader.push({
                  ...vouchByLeaderItem,
                  farmerId: checkFarmer.id,
                  vouchByLeadersAttachment: entryAttachment?.fileName,
                })
              } else {
                tmpVouchLeader.push({
                  ...vouchByLeaderItem,
                  farmerId: checkFarmer.id,
                })
              }
            }
          }

          const updateVouchLeader = await FarmerVouchLeader.updateOrCreateMany(
            'identifier',
            tmpVouchLeader
          )

          const mappedVouchLeaderId = updateVouchLeader.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerVouchLeader.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedVouchLeaderId)
            .delete()
        }
      }

      if (vouchByMao) {
        if (vouchByMao.length === 0) {
          await FarmerVouchMao.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpVouchMao = [] as any

          // CHECK UPLOADED FILE
          for (const vouchByMaoItem of vouchByMao) {
            if (vouchByMaoItem.identifier) {
              const entryAttachment = request.file(`vouchByMao_${vouchByMaoItem.identifier}`, {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              })

              await entryAttachment?.moveToDisk('./users/vouchbymao')

              if (entryAttachment?.fileName) {
                tmpVouchMao.push({
                  ...vouchByMaoItem,
                  farmerId: checkFarmer.id,
                  vouchByMaosAttachment: entryAttachment?.fileName,
                })
              } else {
                tmpVouchMao.push({
                  ...vouchByMaoItem,
                  farmerId: checkFarmer.id,
                })
              }
            }
          }

          const updateVouchMao = await FarmerVouchMao.updateOrCreateMany('identifier', tmpVouchMao)

          const mappedVouchMaoId = updateVouchMao.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerVouchMao.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedVouchMaoId)
            .delete()
        }
      }

      if (referrer) {
        if (referrer.length === 0) {
          await FarmerReferrer.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpReferrer = [] as any

          // CHECK UPLOADED FILE
          for (const referrerItem of referrer) {
            if (referrerItem.identifier) {
              const entryAttachment = request.file(`referrer_${referrerItem.identifier}`, {
                size: '10mb',
                extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
              })

              await entryAttachment?.moveToDisk('./users/referrer')

              if (entryAttachment?.fileName) {
                tmpReferrer.push({
                  ...referrerItem,
                  farmerId: checkFarmer.id,
                  referrerAttachment: entryAttachment?.fileName,
                })
              } else {
                tmpReferrer.push({
                  ...referrerItem,
                  farmerId: checkFarmer.id,
                })
              }
            }
          }

          const updateReferrer = await FarmerReferrer.updateOrCreateMany('identifier', tmpReferrer)

          const mappedReferrerId = updateReferrer.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerReferrer.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedReferrerId)
            .delete()
        }
      }

      if (mobileDeviceBrand !== undefined || mobileDeviceModel !== undefined) {
        await FarmerMobileDevice.updateOrCreate(
          {
            farmerId: checkFarmer.id,
          },
          {
            farmerId: checkFarmer.id,
            mobileDeviceBrand,
            mobileDeviceModel,
          }
        )
      }

      if (farmerUtilities) {
        if (farmerUtilities.length === 0) {
          await FarmerUtility.query().where('farmerId', checkFarmer.id).delete()
        } else {
          let tmpFarmerUtilities = [] as any

          // CHECK UPLOADED FILE
          for (const farmerUtilitiesItem of farmerUtilities) {
            if (farmerUtilitiesItem.identifier) {
              const entryAttachment = request.file(
                `farmerUtilities_${farmerUtilitiesItem.identifier}`,
                {
                  size: '10mb',
                  extnames: ['jpg', 'png', 'gif', 'jpeg', 'webp'],
                }
              )

              await entryAttachment?.moveToDisk('./users/utility')

              if (entryAttachment?.fileName) {
                tmpFarmerUtilities.push({
                  ...farmerUtilitiesItem,
                  farmerId: checkFarmer.id,
                  bill: entryAttachment?.fileName,
                })
              } else {
                tmpFarmerUtilities.push({
                  ...farmerUtilitiesItem,
                  farmerId: checkFarmer.id,
                })
              }
            }
          }

          const updateFarmerUtilities = await FarmerUtility.updateOrCreateMany(
            'identifier',
            tmpFarmerUtilities
          )

          const mappedFarmerUtilitiesId = updateFarmerUtilities.map((item) => item.id)

          // DELETE NON EXISTING ENTRY
          await FarmerUtility.query()
            .where('farmerId', checkFarmer.id)
            .whereNotIn('id', mappedFarmerUtilitiesId)
            .delete()
        }
      }
    }

    const checkFarmerInfo = await FarmerInfo.firstOrCreate(
      {
        farmerId: checkFarmer.id,
      },
      {
        farmerId: checkFarmer.id,
        farmAddress,
        farmAddressHouseNumber,
        farmAddressStreet,
        farmAddressProvince,
        farmAddressCity,
        farmAddressBarangay,
        farmAddressZipCode,
        farmAddressCountry,
        farmOwnership: farmOwnership as FarmOwnership,
        otherFarmOwnership,
        waterSource: waterSource,
        fertilizerUsed: fertilizerUsed as EFertilizerUsed,
        pesticideUsed: pesticideUsed as EPesticideUsed,
        farmImplements: farmImplements,
        averageYieldPerYear: averageYieldPerYear,
        farmArea,
        nationality,
        yearResiding,
        residenceOwnership,
        otherMobileNumber,
        priceBasedBy,
        purchaserSellingLocation,
        purchaserFullname,
        purchaserContactNumber,
        monthlyGrossIncome,
        isMemberOfOrganization,
        organizationName,
        organizationPosition,
        hasPastFarmLoans,
        pastFarmLoans,
        hasPastFarmLoanPaid,
        hasNeedFarmLoan,
        needFarmLoanReason,
        isInterestedToSellAtTradingPost,
      }
    )

    if (!checkFarmerInfo.$isLocal) {
      if (farmOwnership || farmOwnership === FarmOwnership.OWNED) {
        checkFarmerInfo.farmOwnership = farmOwnership as FarmOwnership
      }

      if (otherFarmOwnership && checkFarmerInfo.farmOwnership === FarmOwnership.OTHERS) {
        checkFarmerInfo.otherFarmOwnership = otherFarmOwnership
      }

      if (checkFarmerInfo.farmOwnership !== FarmOwnership.OTHERS) {
        checkFarmerInfo.otherFarmOwnership = null
      }

      if (waterSource !== undefined) {
        checkFarmerInfo.waterSource = waterSource
      }

      if (fertilizerUsed || fertilizerUsed === EFertilizerUsed.INORGANIC) {
        checkFarmerInfo.fertilizerUsed = fertilizerUsed as EFertilizerUsed
      }

      if (pesticideUsed || pesticideUsed === EPesticideUsed.INORGANIC) {
        checkFarmerInfo.pesticideUsed = pesticideUsed as EPesticideUsed
      }

      if (farmImplements !== undefined) {
        checkFarmerInfo.farmImplements = farmImplements
      }

      if (averageYieldPerYear !== undefined) {
        checkFarmerInfo.averageYieldPerYear = averageYieldPerYear || 0
      }

      if (monthlyGrossIncome !== undefined || monthlyGrossIncome === 0) {
        checkFarmerInfo.monthlyGrossIncome = monthlyGrossIncome || 0
      }

      if (isMemberOfOrganization || isMemberOfOrganization === EMemberOfOrganization.NO) {
        checkFarmerInfo.isMemberOfOrganization = isMemberOfOrganization
      }

      if (organizationName !== undefined) {
        checkFarmerInfo.organizationName = organizationName
      }

      if (organizationPosition !== undefined) {
        checkFarmerInfo.organizationPosition = organizationPosition
      }

      if (hasPastFarmLoans || hasPastFarmLoans === EHasPastFarmLoans.NO) {
        checkFarmerInfo.hasPastFarmLoans = hasPastFarmLoans
      }
      if (pastFarmLoans !== undefined) {
        checkFarmerInfo.pastFarmLoans = pastFarmLoans
      }

      if (hasPastFarmLoanPaid || hasPastFarmLoanPaid === EHasPastFarmLoanPaid.NO) {
        checkFarmerInfo.hasPastFarmLoanPaid = hasPastFarmLoanPaid
      }

      if (hasNeedFarmLoan || hasNeedFarmLoan === EHasNeedFarmLoan.NO) {
        checkFarmerInfo.hasNeedFarmLoan = hasNeedFarmLoan
      }

      if (needFarmLoanReason !== undefined) {
        checkFarmerInfo.needFarmLoanReason = needFarmLoanReason
      }

      if (
        isInterestedToSellAtTradingPost ||
        isInterestedToSellAtTradingPost === EIsInterestedToSellAtTradingPost.NO
      ) {
        checkFarmerInfo.isInterestedToSellAtTradingPost = isInterestedToSellAtTradingPost
      }

      if (farmArea !== undefined) {
        checkFarmerInfo.farmArea = farmArea || 0
      }

      if (farmAddress !== undefined) {
        checkFarmerInfo.farmAddress = farmAddress
      }

      if (farmAddressHouseNumber !== undefined) {
        checkFarmerInfo.farmAddressHouseNumber = farmAddressHouseNumber
      }

      if (farmAddressProvince !== undefined) {
        checkFarmerInfo.farmAddressProvince = farmAddressProvince
      }

      if (farmAddressStreet !== undefined) {
        checkFarmerInfo.farmAddressStreet = farmAddressStreet
      }

      if (farmAddressCity !== undefined) {
        checkFarmerInfo.farmAddressCity = farmAddressCity
      }

      if (farmAddressBarangay !== undefined) {
        checkFarmerInfo.farmAddressBarangay = farmAddressBarangay
      }

      if (farmAddressZipCode !== undefined) {
        checkFarmerInfo.farmAddressZipCode = farmAddressZipCode
      }

      if (farmAddressCountry !== undefined) {
        checkFarmerInfo.farmAddressCountry = farmAddressCountry
      }

      if (nationality !== undefined) {
        checkFarmerInfo.nationality = nationality
      }

      if (residenceOwnership !== undefined) {
        checkFarmerInfo.residenceOwnership = residenceOwnership
      }

      if (otherMobileNumber !== undefined) {
        checkFarmerInfo.otherMobileNumber = otherMobileNumber
      }

      if (yearResiding !== undefined || yearResiding === 0) {
        checkFarmerInfo.yearResiding = yearResiding || 0
      }

      if (priceBasedBy !== undefined) {
        checkFarmerInfo.priceBasedBy = priceBasedBy
      }

      if (purchaserSellingLocation !== undefined) {
        checkFarmerInfo.purchaserSellingLocation = purchaserSellingLocation
      }

      if (purchaserFullname !== undefined) {
        checkFarmerInfo.purchaserFullname = purchaserFullname
      }

      if (purchaserContactNumber !== undefined) {
        checkFarmerInfo.purchaserContactNumber = purchaserContactNumber
      }
    }

    const checkFarmerInsurance = await FarmerInsurance.firstOrCreate(
      {
        farmerId: checkFarmer.id,
      },
      {
        farmerId: checkFarmer.id,
        landCategory,
        crop,
        phase,
        ownerCultivator,
        tenant,
        cltEp,
        lessee,
        naturalDisasterCover,
        multiRiskCover,
        desiredAmountCover,
        additionalAmountCover,
        transplantingDate,
        harvestDate,
        sowingDate,
        seedbedding,
        planting,
        plantCare,
        insurancePremium,
        insuranceLocationPlan: insuranceLocationPlan?.fileName,
      }
    )

    if (!checkFarmerInsurance.$isLocal) {
      if (landCategory !== undefined) {
        checkFarmerInsurance.landCategory = landCategory
      }

      if (crop !== undefined) {
        checkFarmerInsurance.crop = crop
      }

      if (phase !== undefined) {
        checkFarmerInsurance.phase = phase
      }

      if (ownerCultivator !== undefined) {
        checkFarmerInsurance.ownerCultivator = ownerCultivator
      }

      if (tenant !== undefined) {
        checkFarmerInsurance.tenant = tenant
      }

      if (cltEp !== undefined) {
        checkFarmerInsurance.cltEp = cltEp
      }

      if (lessee !== undefined) {
        checkFarmerInsurance.lessee = lessee
      }

      if (naturalDisasterCover !== undefined) {
        checkFarmerInsurance.naturalDisasterCover = naturalDisasterCover
      }

      if (multiRiskCover !== undefined) {
        checkFarmerInsurance.multiRiskCover = multiRiskCover
      }

      if (desiredAmountCover !== undefined) {
        checkFarmerInsurance.desiredAmountCover = desiredAmountCover
      }

      if (additionalAmountCover !== undefined) {
        checkFarmerInsurance.additionalAmountCover = additionalAmountCover
      }

      if (transplantingDate !== undefined) {
        checkFarmerInsurance.transplantingDate = transplantingDate
      }

      if (harvestDate !== undefined) {
        checkFarmerInsurance.harvestDate = harvestDate
      }

      if (sowingDate !== undefined) {
        checkFarmerInsurance.sowingDate = sowingDate
      }

      if (seedbedding !== undefined) {
        checkFarmerInsurance.seedbedding = JSON.stringify({ data: seedbedding })
      }

      if (planting !== undefined) {
        checkFarmerInsurance.planting = JSON.stringify({ data: planting })
      }

      if (plantCare !== undefined) {
        checkFarmerInsurance.plantCare = JSON.stringify({ data: plantCare })
      }

      if (insurancePremium !== undefined) {
        checkFarmerInsurance.insurancePremium = insurancePremium
      }

      if (insuranceLocationPlan !== undefined) {
        checkFarmerInsurance.insuranceLocationPlan = insuranceLocationPlan?.fileName
      }
    }

    if (email !== undefined) {
      findUser.email = email
    }

    await findUser.save()

    if (!checkFarmer.$isLocal) {
      await checkFarmer.save()
    }

    if (!checkFarmerInfo.$isLocal) {
      await checkFarmerInfo.save()
    }

    if (!checkFarmerInsurance.$isLocal) {
      await checkFarmerInsurance.save()
    }

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE ENTRY (UPDATE FARMER)',
      model: 'Farmer',
    })

    await triggerCreditScoreComputation(findUser.id)

    if (userImage) {
      const findFarmerLoanRequirementItem = await FarmerLoanRequirementItem.query()
        .where('farmerId', checkFarmer.id)
        .where('name', '2x2')
        .first()

      if (findFarmerLoanRequirementItem) {
        findFarmerLoanRequirementItem.isCompleted = EFarmerLoanRequirementItemStatus.COMPLETED
        await findFarmerLoanRequirementItem.save()
      }

      await triggerLoanRequirementComputation(checkFarmer.id, '2x2')
    }

    if (governmentIdentification) {
      if (governmentIdentification.length > 0) {
        const findFarmerLoanRequirementItem = await FarmerLoanRequirementItem.query()
          .where('farmerId', checkFarmer.id)
          .where('name', 'government_id')
          .first()

        if (findFarmerLoanRequirementItem) {
          findFarmerLoanRequirementItem.isCompleted = EFarmerLoanRequirementItemStatus.COMPLETED
          await findFarmerLoanRequirementItem.save()
        }

        await triggerLoanRequirementComputation(checkFarmer.id, 'government_id')
      }

      governmentIdentification.forEach(async (item) => {
        if (item.governmentIdType === GovernmentIdentificationType.TIN) {
          const findFarmerLoanRequirementItem = await FarmerLoanRequirementItem.query()
            .where('farmerId', checkFarmer.id)
            .where('name', 'bir_tin')
            .first()

          if (findFarmerLoanRequirementItem) {
            findFarmerLoanRequirementItem.isCompleted = EFarmerLoanRequirementItemStatus.COMPLETED
            await findFarmerLoanRequirementItem.save()
          }
          await triggerLoanRequirementComputation(checkFarmer.id, 'bir_tin')
        }

        if (item.governmentIdType === GovernmentIdentificationType.RSBSA) {
          const findFarmerLoanRequirementItem = await FarmerLoanRequirementItem.query()
            .where('farmerId', checkFarmer.id)
            .where('name', 'rsbsa_id')
            .first()

          if (findFarmerLoanRequirementItem) {
            findFarmerLoanRequirementItem.isCompleted = EFarmerLoanRequirementItemStatus.COMPLETED
            await findFarmerLoanRequirementItem.save()
          }

          await triggerLoanRequirementComputation(checkFarmer.id, 'rsbsa_id')
        }
      })
    }

    return response.json({
      status: 1,
      message: 'Farmer updated successfully!',
    })
  }

  public async viewFarmerByIdByAdmin({ request, response }: HttpContextContract) {
    const { userId } = request.params()

    const findUser = await User.query()
      .preload('farmer', (subQuery) => {
        subQuery
          .preload('farmerInfo')
          .preload('cropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('subCropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
          .preload('familyProfiles')
          .preload('farmerInsurance')
          .preload('farmerDataPrivacy')
          .preload('farmerCreditScore')
          .preload('farmerVehicles')
          .preload('governmentIdentifications')
          .preload('realProperties')
          .preload('farmerBankDetails')
          .preload('farmerCharacterReferences')
          .preload('farmerVouchLeaders')
          .preload('farmerVouchMaos')
          .preload('farmerReferrers')
          .preload('farmerUtilities')
          .preload('farmerMobileDevice')
          .preload('chemicals', (subQuery) => {
            subQuery.preload('chemical', (subQuery) => {
              subQuery
                .preload('chemicalSubcategory')
                .preload('chemicalModeOfAction')
                .preload('chemicalActiveIngredients', (subQuery) => {
                  subQuery.preload('chemicalActiveIngredient')
                })
            })
          })
          .preload('seeds', (subQuery) => {
            subQuery.preload('seed')
          })
          .preload('seedSubcategories', (subQuery) => {
            subQuery.preload('seedSubcategory')
          })
          .preload('fertilizers', (subQuery) => {
            subQuery.preload('fertilizer')
          })
          .preload('farmerLandbankRequirements', (subQuery) => {
            subQuery.preload('processedBy')
          })
          .preload('farmerLoanRequirements')
      })
      .preload('farmPlans', (subQuery) => {
        subQuery
          .preload('crop')
          .preload('agronomistUser', (subQuery) => {
            subQuery.preload('agronomist')
          })
          .preload('user', (subQuery) => {
            subQuery.preload('farmer', (subQuery) => {
              subQuery.preload('farmerInfo')
            })
          })
          .preload('farmPlanItems', (subQuery) => {
            subQuery.preload('farmPlanSubItems', (subQuery) => {
              subQuery.preload('marketplaceProduct')
            })
          })
      })
      .preload('wallet')
      .where('userType', UserType.FARMER)
      .where('id', userId)

    const findLoanPeriodTracker = await LoanPeriodTracker.query()
      .preload('topupRequest')
      .where('userId', userId)
      .where('status', LoanPeriodTrackerStatus.ONGOING)
      .orderBy('id', 'desc')
      .first()

    return response.json({
      status: 1,
      data:
        findUser.length === 0
          ? null
          : {
              ...findUser[0].toJSON(),
              loanPeriodTracker: findLoanPeriodTracker,
            },
    })
  }

  public async account_update_password({ auth, request, response }: HttpContextContract) {
    if (!auth?.user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User not found!',
      })
    }

    const { currentPassword, newPassword, confirmPassword } = request.body()

    if (newPassword !== confirmPassword) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Password not match!',
      })
    }

    if (!(await Hash.verify(auth?.user?.password, currentPassword))) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Incorrect current password!',
      })
    }

    auth.user.password = newPassword

    await auth.user.save()

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE PASSWORD (USER)',
      model: 'User',
      data: JSON.stringify({ password: 'REDACTED' }),
      dataJson: { userId: auth?.user?.id },
    })

    return response.json({
      status: 1,
      message: 'User password updated successfully!',
    })
  }

  public async farmer_approve({ auth, request, response }: HttpContextContract) {
    const { userId } = request.body()

    const checkUser = await User.find(userId)

    if (!checkUser) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User doesnt exist!',
      })
    }

    checkUser.status = UserStatusType.ACTIVATED

    await checkUser.save()

    if (checkUser.userType === UserType.FARMER) {
      const farmer = await Farmer.findBy('userId', checkUser.id)

      if (farmer) {
        await FarmerTransaction.firstOrCreate(
          {
            farmerId: farmer.id,
          },
          {
            farmerId: farmer.id,
            loanCycle: 1,
            beforeLoanStartAt: farmer.createdAt,
            tradingPostTotal: 0,
            tradingPostBeforeLoanTotal: 0,
            tradingPostAfterLoanTotal: 0,
            tradingPostDuringLoanTotal: 0,
            marketplaceTotal: 0,
            marketplaceAfterLoanTotal: 0,
            marketplaceBeforeLoanTotal: 0,
            marketplaceDuringLoanTotal: 0,
            salesTotal: 0,
            salesAfterLoanTotal: 0,
            salesBeforeLoanTotal: 0,
            salesDuringLoanTotal: 0,
          }
        )
      }
    }

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE ENTRY (APPROVE USER)',
      model: 'User',
      dataJson: { userId },
    })

    return response.json({
      status: 1,
      message: 'User approved successfully!',
    })
  }

  public async farmer_reject({ auth, request, response }: HttpContextContract) {
    const { userId } = request.body()

    const checkUser = await User.find(userId)

    if (!checkUser) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User doesnt exist!',
      })
    }

    checkUser.status = UserStatusType.BLOCKED

    await checkUser.save()

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE ENTRY (REJECT USER)',
      model: 'User',
      dataJson: { userId },
    })

    return response.json({
      status: 1,
      message: 'User rejected successfully!',
    })
  }

  public async update_credit_score({ auth, request, response }: HttpContextContract) {
    if (!auth?.user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User not found!',
      })
    }

    const {
      farmerId,
      landPreparation,
      landPreparationRemarks,
      geoTagging,
      geoTaggingRemarks,
      followedSeedsPerArea,
      gapCompliance,
      harvestForecast,
      harvestForecastRemarks,
      yieldAchievementOptionOne,
      yieldAchievementOptionOneRemarks,
      yieldAchievementOptionTwo,
      yieldAchievementOptionTwoRemarks,
      yieldAchievementOptionThree,
      yieldAchievementOptionThreeRemarks,
      fertilizationScheduleFirstVisit,
      fertilizationScheduleFirstVisitRemarks,
      fertilizationScheduleSecondVisit,
      fertilizationScheduleSecondVisitRemarks,
      fertilizationScheduleThirdVisit,
      fertilizationScheduleThirdVisitRemarks,
      fertilizationVolumeFirstVisit,
      fertilizationVolumeFirstVisitRemarks,
      fertilizationVolumeSecondVisit,
      fertilizationVolumeSecondVisitRemarks,
      fertilizationVolumeThirdVisit,
      fertilizationVolumeThirdVisitRemarks,
      cropProtectionScheduleFirstVisit,
      cropProtectionScheduleFirstVisitRemarks,
      cropProtectionScheduleSecondVisit,
      cropProtectionScheduleSecondVisitRemarks,
      cropProtectionScheduleThirdVisit,
      cropProtectionScheduleThirdVisitRemarks,
      cropProtectionVolumeFirstVisit,
      cropProtectionVolumeFirstVisitRemarks,
      cropProtectionVolumeSecondVisit,
      cropProtectionVolumeSecondVisitRemarks,
      cropProtectionVolumeThirdVisit,
      cropProtectionVolumeThirdVisitRemarks,
    } = await request.validate(UpdateAgricultureActivityValidator)

    const findFarmer = await Farmer.find(farmerId)

    if (!findFarmer) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Farmer not found!',
      })
    }

    const checkCreditScore = await CreditScoring.firstOrCreate(
      {
        farmerId: findFarmer.id,
      },
      {
        farmerId: findFarmer.id,
      }
    )

    let message = [] as any[]

    if (harvestForecast || harvestForecast === 0) {
      checkCreditScore.harvestForecast = harvestForecast
      message.push(
        harvestForecast
          ? 'Providing Harvest forecast has been successfully checked'
          : 'Providing Harvest forecast has been successfully unchecked'
      )
    }

    if (harvestForecastRemarks) {
      checkCreditScore.harvestForecastRemarks = harvestForecastRemarks
      message.push('Updated Harvest Forecast Remarks')
    }

    if (landPreparation || landPreparation === 0) {
      checkCreditScore.landPreparation = landPreparation
      message.push(
        landPreparation
          ? 'Providing Land Preparation has been successfully checked'
          : 'Providing Land Preparation has been successfully unchecked'
      )
    }

    if (landPreparationRemarks) {
      checkCreditScore.landPreparationRemarks = landPreparationRemarks
      message.push('Updated Land Preparation Remarks')
    }

    if (geoTagging || geoTagging === 0) {
      checkCreditScore.geoTagging = geoTagging
      message.push(
        geoTagging
          ? 'Providing Land Preparation has been successfully checked'
          : 'Providing Land Preparation has been successfully unchecked'
      )
    }

    if (geoTaggingRemarks) {
      checkCreditScore.geoTaggingRemarks = geoTaggingRemarks
      message.push('Updated Land Preparation Remarks')
    }

    if (followedSeedsPerArea || followedSeedsPerArea === 0) {
      checkCreditScore.followedSeedsPerArea = followedSeedsPerArea
      message.push(
        followedSeedsPerArea
          ? 'Providing Followed Seeds Per Area has been successfully checked'
          : 'Providing Followed Seeds Per Area has been successfully unchecked'
      )
    }

    if (gapCompliance || gapCompliance === 0) {
      checkCreditScore.gapCompliance = gapCompliance
      message.push(
        gapCompliance
          ? 'GAP Compliance has been successfully checked'
          : 'GAP Compliance has been successfully unchecked'
      )
    }

    if (fertilizationScheduleFirstVisit || fertilizationScheduleFirstVisit === 0) {
      checkCreditScore.fertilizationScheduleFirstVisit = fertilizationScheduleFirstVisit
      message.push(
        fertilizationScheduleFirstVisit
          ? 'Providing Fertilization Schedule First Visit has been successfully checked'
          : 'Providing Fertilization Schedule First Visit has been successfully unchecked'
      )
    }

    if (fertilizationScheduleFirstVisitRemarks) {
      checkCreditScore.fertilizationScheduleFirstVisitRemarks =
        fertilizationScheduleFirstVisitRemarks
      message.push('Updated Fertilization Schedule First Visit Remarks')
    }

    if (fertilizationScheduleSecondVisit || fertilizationScheduleSecondVisit === 0) {
      checkCreditScore.fertilizationScheduleSecondVisit = fertilizationScheduleSecondVisit
      message.push(
        fertilizationScheduleSecondVisit
          ? 'Providing Fertilization Schedule Second Visit has been successfully checked'
          : 'Providing Fertilization Schedule Second Visit has been successfully unchecked'
      )
    }

    if (fertilizationScheduleSecondVisitRemarks) {
      checkCreditScore.fertilizationScheduleSecondVisitRemarks =
        fertilizationScheduleSecondVisitRemarks
      message.push('Updated Fertilization Schedule Second Visit Remarks')
    }

    if (fertilizationScheduleThirdVisit || fertilizationScheduleThirdVisit === 0) {
      checkCreditScore.fertilizationScheduleThirdVisit = fertilizationScheduleThirdVisit
      message.push(
        fertilizationScheduleThirdVisit
          ? 'Providing Fertilization Schedule Third Visit has been successfully checked'
          : 'Providing Fertilization Schedule Third Visit has been successfully unchecked'
      )
    }

    if (fertilizationScheduleThirdVisitRemarks) {
      checkCreditScore.fertilizationScheduleThirdVisitRemarks =
        fertilizationScheduleThirdVisitRemarks
      message.push('Updated Fertilization Schedule Third Visit Remarks')
    }

    if (fertilizationVolumeFirstVisit || fertilizationVolumeFirstVisit === 0) {
      checkCreditScore.fertilizationVolumeFirstVisit = fertilizationVolumeFirstVisit
      message.push(
        fertilizationVolumeFirstVisit
          ? 'Providing Fertilization Volume First Visit has been successfully checked'
          : 'Providing Fertilization Volume First Visit has been successfully unchecked'
      )
    }

    if (fertilizationVolumeFirstVisitRemarks) {
      checkCreditScore.fertilizationVolumeFirstVisitRemarks = fertilizationVolumeFirstVisitRemarks
      message.push('Updated Fertilization Volume First Visit Remarks')
    }

    if (fertilizationVolumeSecondVisit || fertilizationVolumeSecondVisit === 0) {
      checkCreditScore.fertilizationVolumeSecondVisit = fertilizationVolumeSecondVisit
      message.push(
        fertilizationVolumeSecondVisit
          ? 'Providing Fertilization Volume Second Visit has been successfully checked'
          : 'Providing Fertilization Volume Second Visit has been successfully unchecked'
      )
    }

    if (fertilizationVolumeSecondVisitRemarks) {
      checkCreditScore.fertilizationVolumeSecondVisitRemarks = fertilizationVolumeSecondVisitRemarks
      message.push('Updated Fertilization Volume Second Visit Remarks')
    }

    if (fertilizationVolumeThirdVisit || fertilizationVolumeThirdVisit === 0) {
      checkCreditScore.fertilizationVolumeThirdVisit = fertilizationVolumeThirdVisit
      message.push(
        fertilizationVolumeThirdVisit
          ? 'Providing Fertilization Volume Third Visit has been successfully checked'
          : 'Providing Fertilization Volume Third Visit has been successfully unchecked'
      )
    }

    if (fertilizationVolumeThirdVisitRemarks) {
      checkCreditScore.fertilizationVolumeThirdVisitRemarks = fertilizationVolumeThirdVisitRemarks
      message.push('Updated Fertilization Volume Third Visit Remarks')
    }

    if (cropProtectionScheduleFirstVisit || cropProtectionScheduleFirstVisit === 0) {
      checkCreditScore.cropProtectionScheduleFirstVisit = cropProtectionScheduleFirstVisit
      message.push(
        cropProtectionScheduleFirstVisit
          ? 'Providing Crop Protection Schedule First Visit has been successfully checked'
          : 'Providing Crop Protection Schedule First Visit has been successfully unchecked'
      )
    }

    if (cropProtectionScheduleFirstVisitRemarks) {
      checkCreditScore.cropProtectionScheduleFirstVisitRemarks =
        cropProtectionScheduleFirstVisitRemarks
      message.push('Updated Crop Protection Schedule First Visit Remarks')
    }

    if (cropProtectionScheduleSecondVisit || cropProtectionScheduleSecondVisit === 0) {
      checkCreditScore.cropProtectionScheduleSecondVisit = cropProtectionScheduleSecondVisit
      message.push(
        cropProtectionScheduleSecondVisit
          ? 'Providing Crop Protection Schedule Second Visit has been successfully checked'
          : 'Providing Crop Protection Schedule Second Visit has been successfully unchecked'
      )
    }

    if (cropProtectionScheduleSecondVisitRemarks) {
      checkCreditScore.cropProtectionScheduleSecondVisitRemarks =
        cropProtectionScheduleSecondVisitRemarks
      message.push('Updated Crop Protection Schedule Second Visit Remarks')
    }

    if (cropProtectionScheduleThirdVisit || cropProtectionScheduleThirdVisit === 0) {
      checkCreditScore.cropProtectionScheduleThirdVisit = cropProtectionScheduleThirdVisit
      message.push(
        cropProtectionScheduleThirdVisit
          ? 'Providing Crop Protection Schedule Third Visit has been successfully checked'
          : 'Providing Crop Protection Schedule Third Visit has been successfully unchecked'
      )
    }

    if (cropProtectionScheduleThirdVisitRemarks) {
      checkCreditScore.cropProtectionScheduleThirdVisitRemarks =
        cropProtectionScheduleThirdVisitRemarks
      message.push('Updated Crop Protection Schedule Third Visit Remarks')
    }

    if (cropProtectionVolumeFirstVisit || cropProtectionVolumeFirstVisit === 0) {
      checkCreditScore.cropProtectionVolumeFirstVisit = cropProtectionVolumeFirstVisit
      message.push(
        cropProtectionVolumeFirstVisit
          ? 'Providing Crop Protection Volume First Visit has been successfully checked'
          : 'Providing Crop Protection Volume First Visit has been successfully unchecked'
      )
    }

    if (cropProtectionVolumeFirstVisitRemarks) {
      checkCreditScore.cropProtectionVolumeFirstVisitRemarks = cropProtectionVolumeFirstVisitRemarks
      message.push('Updated Crop Protection Volume First Visit Remarks')
    }

    if (cropProtectionVolumeSecondVisit || cropProtectionVolumeSecondVisit === 0) {
      checkCreditScore.cropProtectionVolumeSecondVisit = cropProtectionVolumeSecondVisit
      message.push(
        cropProtectionVolumeSecondVisit
          ? 'Providing Crop Protection Volume Second Visit has been successfully checked'
          : 'Providing Crop Protection Volume Second Visit has been successfully unchecked'
      )
    }

    if (cropProtectionVolumeSecondVisitRemarks) {
      checkCreditScore.cropProtectionVolumeSecondVisitRemarks =
        cropProtectionVolumeSecondVisitRemarks
      message.push('Updated Crop Protection Volume Second Visit Remarks')
    }

    if (cropProtectionVolumeThirdVisit || cropProtectionVolumeThirdVisit === 0) {
      checkCreditScore.cropProtectionVolumeThirdVisit = cropProtectionVolumeThirdVisit
      message.push(
        cropProtectionVolumeThirdVisit
          ? 'Providing Crop Protection Volume Third Visit has been successfully checked'
          : 'Providing Crop Protection Volume Third Visit has been successfully unchecked'
      )
    }

    if (cropProtectionVolumeThirdVisitRemarks) {
      checkCreditScore.cropProtectionVolumeThirdVisitRemarks = cropProtectionVolumeThirdVisitRemarks
      message.push('Updated Crop Protection Volume Third Visit Remarks')
    }

    if (yieldAchievementOptionOne || yieldAchievementOptionOne === 0) {
      checkCreditScore.yieldAchievementOptionOne = yieldAchievementOptionOne
      message.push('Target Yield Achievement updated successfully')
    }

    if (yieldAchievementOptionTwo || yieldAchievementOptionTwo === 0) {
      checkCreditScore.yieldAchievementOptionTwo = yieldAchievementOptionTwo
      message.push('Target Yield Achievement updated successfully')
    }

    if (yieldAchievementOptionThree || yieldAchievementOptionThree === 0) {
      checkCreditScore.yieldAchievementOptionThree = yieldAchievementOptionThree
      message.push('Target Yield Achievement updated successfully')
    }

    if (yieldAchievementOptionOneRemarks) {
      checkCreditScore.yieldAchievementOptionOneRemarks = yieldAchievementOptionOneRemarks
      message.push('Updated Yield Achievement Option One Remarks')
    }

    if (yieldAchievementOptionTwoRemarks) {
      checkCreditScore.yieldAchievementOptionTwoRemarks = yieldAchievementOptionTwoRemarks
      message.push('Updated Yield Achievement Option Two Remarks')
    }

    if (yieldAchievementOptionThreeRemarks) {
      checkCreditScore.yieldAchievementOptionThreeRemarks = yieldAchievementOptionThreeRemarks
      message.push('Updated Yield Achievement Option Three Remarks')
    }

    if (
      checkCreditScore.yieldAchievementOptionOne ||
      checkCreditScore.yieldAchievementOptionTwo ||
      checkCreditScore.yieldAchievementOptionThree
    ) {
      checkCreditScore.yieldAchievement = Status.ACTIVE
    } else {
      checkCreditScore.yieldAchievement = Status.INACTIVE
    }

    await checkCreditScore.save()

    await triggerCreditScoreComputation(findFarmer.userId)

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE CREDIT SCORING (USER)',
      model: 'CreditScoring',
      data: JSON.stringify({
        farmerId,
        harvestForecast,
        yieldAchievementOptionOne,
        yieldAchievementOptionTwo,
        yieldAchievementOptionThree,
      }),
    })

    return response.json({
      status: 1,
      message: message,
    })
  }

  public async farmer_generate_qr({ auth, request, response }: HttpContextContract) {
    const { farmerId } = request.body()

    const checkFarmer = await Farmer.find(farmerId)

    if (!checkFarmer) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'User doesnt exist!',
      })
    }

    if (checkFarmer.qrCode) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Farmer QR Code already exists!',
      })
    }

    const qrCode = (
      new Date().getTime().toString(36) + Math.random().toString(36).slice(2)
    ).toUpperCase()

    checkFarmer.qrCode = qrCode

    await checkFarmer.save()

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE ENTRY (GENERATE QR CODE)',
      model: 'Farmer',
    })

    return response.json({
      status: 1,
      message: 'Farmer QR Code generated successfully!',
    })
  }
}
