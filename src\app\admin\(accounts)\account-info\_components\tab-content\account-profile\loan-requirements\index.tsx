'use client';

import { Timeline, TimelineConnector, TimelineHeader, TimelineIcon, TimelineItem } from '@material-tailwind/react';
import { format } from 'date-fns';
import { CircleCheckIcon, CircleXIcon } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { parseAsInteger, useQueryState } from 'nuqs';
import { MdRadioButtonChecked } from 'react-icons/md';

import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion-custom';
import { Button } from '@/components/ui/button';

import { useGlobalStatePersist } from '@/lib/store/persist';
import { cn } from '@/lib/utils';

import ItemList from './components/item-list';
import { useLoanRequirements } from './hooks/useLoanRequirements';
import { LOAN_REQUIREMENTS_STAGE } from './types/loan-requirements.types';

export default function LoanRequirements() {
  const gStateP = useGlobalStatePersist();
  const router = useRouter();

  const [userId] = useQueryState('id', parseAsInteger.withDefault(0));
  const farmerId = gStateP.selected.account.info['farmer']['id'].value;
  const loanReqs = useLoanRequirements(farmerId);

  return (
    <div>
      <Timeline>
        <Accordion className="" type="single" collapsible>
          {loanReqs.loanRequirements &&
            !loanReqs.isLoading &&
            loanReqs.loanRequirements.data.map((reqsData) => {
              const total = Object.keys(reqsData.stage_summary).length;
              const isComplete = total === reqsData.stage_count;

              // Stages
              const isStage1 = reqsData.stage === 1;
              const isStage2 = reqsData.stage === 2;
              const isStage3 = reqsData.stage === 3;
              const isStage4 = reqsData.stage === 4;

              const onViewDetails = () => {
                router.push(`./loan-requirements-details/?id=${userId}&fid=${farmerId}&stage=${reqsData.stage}`);
              };

              if (isStage4) {
                return (
                  <AccordionItem key={reqsData.id} className="" value={`loan-stage-${reqsData.stage}`}>
                    <TimelineItem className={cn('min-h-[5.4rem]')}>
                      <AccordionTrigger className="">
                        <TimelineHeader
                          className={cn(
                            'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                            isComplete ? 'bg-gray-200' : 'bg-white',
                          )}
                        >
                          <TimelineIcon className="p-3" variant="ghost" color={isComplete ? 'green' : 'orange'}>
                            {isComplete ? (
                              <CircleCheckIcon className="size-5" />
                            ) : (
                              <MdRadioButtonChecked className="size-5" />
                            )}
                          </TimelineIcon>
                          <div className="flex w-full items-center justify-between">
                            <div className="text-left">
                              <div className="text-lg font-semibold text-kitaph-primary">
                                {LOAN_REQUIREMENTS_STAGE[reqsData.stage]}
                              </div>

                              {reqsData.stage_started_at ? (
                                <div className="text-xs font-light text-gray-500">{`Start Date at ${format(new Date(reqsData.stage_started_at), 'MMM dd, yyyy')}`}</div>
                              ) : (
                                <div className="text-xs font-light text-gray-500">Not Started yet</div>
                              )}
                            </div>
                            <div className="text-lg font-bold text-kitaph-primary">
                              {reqsData.stage_count}/{total}
                            </div>
                          </div>
                        </TimelineHeader>
                      </AccordionTrigger>

                      <AccordionContent className="relative z-10 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                        {isStage4 && (
                          <div className="grid grid-cols-2 gap-8">
                            <div className="grid gap-2">
                              <ItemList
                                isComplete={reqsData.stage_summary['agri_inputs_distribution']}
                                description="Agri-inputs Distribution to Farmers"
                              />
                              <ItemList
                                isComplete={reqsData.stage_summary['submission_of_soa']}
                                description="Submission of Delivery Receipts/Sales Invoice/Statement of Account to LBP"
                              />
                            </div>
                            <div className="grid gap-2">
                              <ItemList
                                isComplete={reqsData.stage_summary['transfer_of_cash']}
                                description="Transfer of Cash to Kita Bank Account from Farmers Account"
                              />
                              <ItemList
                                isComplete={reqsData.stage_summary['amount_to_be_credited']}
                                description=" Amount to be Credited to Kita Bank Account"
                              />
                            </div>
                          </div>
                        )}

                        <div className="mt-4 grid place-items-center">
                          <Button onClick={onViewDetails}>View Details</Button>
                        </div>
                      </AccordionContent>
                    </TimelineItem>
                  </AccordionItem>
                );
              }

              return (
                <AccordionItem key={reqsData.id} className="" value={`loan-stage-${reqsData.stage}`}>
                  <TimelineItem className={cn('min-h-[5.4rem]')}>
                    <TimelineConnector className="!w-[78px]" />
                    <AccordionTrigger className="">
                      <TimelineHeader
                        className={cn(
                          'relative rounded-xl border border-kitaph-primary/30 py-3 px-4 shadow-lg shadow-blue-gray-900/5',
                          isComplete ? 'bg-gray-200' : 'bg-white',
                        )}
                      >
                        <TimelineIcon className="p-3" variant="ghost" color={isComplete ? 'green' : 'orange'}>
                          {isComplete ? (
                            <CircleCheckIcon className="size-5" />
                          ) : (
                            <MdRadioButtonChecked className="size-5" />
                          )}
                        </TimelineIcon>
                        <div className="flex w-full items-center justify-between">
                          <div className="text-left">
                            <div className="text-lg font-semibold text-kitaph-primary">
                              {LOAN_REQUIREMENTS_STAGE[reqsData.stage]}
                            </div>

                            {reqsData.stage_started_at ? (
                              <div className="text-xs font-light text-gray-500">{`Start Date at ${format(new Date(reqsData.stage_started_at), 'MMM dd, yyyy')}`}</div>
                            ) : (
                              <div className="text-xs font-light text-gray-500">Not Started yet</div>
                            )}
                          </div>
                          <div className="text-lg font-bold text-kitaph-primary">
                            {reqsData.stage_count}/{total}
                          </div>
                        </div>
                      </TimelineHeader>
                    </AccordionTrigger>

                    <AccordionContent className="relative z-10 mb-4 mt-2 grid gap-4 rounded-xl border border-kitaph-primary/30 bg-white px-20 py-6 text-left shadow-lg shadow-blue-gray-900/5">
                      {isStage1 && (
                        <div className="grid grid-cols-2 gap-8">
                          <div className="grid gap-2">
                            <ItemList isComplete={reqsData.stage_summary['2x2']} description="2x2 Picture" />
                            <ItemList isComplete={reqsData.stage_summary['bir_tin']} description="BIR TIN" />
                            <ItemList isComplete={reqsData.stage_summary['rsbsa_id']} description="RSBSA ID" />
                            <ItemList
                              isComplete={reqsData.stage_summary['government_id']}
                              description="Government Issued ID"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['barangay_clearance']}
                              description="Barangay Clearance"
                            />
                          </div>
                          <div className="grid gap-2">
                            <ItemList
                              isComplete={reqsData.stage_summary['land_agreement']}
                              description="Photocopy of Land / Lease Agreement"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['kita_loan_form']}
                              description="Kita Loan Form"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['lbp_agrisenso_loan_form']}
                              description="LBP Agrisenso Loan Form"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['lbp_data_privacy']}
                              description="LBP Data Privacy and CIS Form"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['farmer_meeting']}
                              description="Farmers Meeting / Financial Literacy"
                            />
                          </div>
                        </div>
                      )}

                      {isStage2 && (
                        <div className="grid grid-cols-2 gap-8">
                          <div className="grid gap-2">
                            <ItemList
                              isComplete={reqsData.stage_summary['nia_certification']}
                              description="NIA Certification"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['ia_certification']}
                              description="IA Certification"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['dar_certification']}
                              description="DAR Certification"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['mao_certification']}
                              description="MAO Certification"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['pcic']}
                              description="Geo Tagging / Farm Picture / PCIC"
                            />
                          </div>
                          <div className="grid gap-2">
                            <ItemList isComplete={reqsData.stage_summary['ptma']} description="PTMA" />
                            <ItemList isComplete={reqsData.stage_summary['msa']} description="MSA" />
                            <ItemList isComplete={reqsData.stage_summary['farm_plan']} description="Farm Plan" />
                            <ItemList
                              isComplete={reqsData.stage_summary['tripartite_agreement']}
                              description="Tripartite Agreement"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['signed_notarized_documents']}
                              description="Signed & Notarized Documents"
                            />
                          </div>
                        </div>
                      )}

                      {isStage3 && (
                        <div className="grid grid-cols-2 gap-8">
                          <div className="grid gap-2">
                            <ItemList
                              isComplete={reqsData.stage_summary['submission_of_documents']}
                              description="Submission of Documents to LBP"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['verification_of_documents']}
                              description="Verification / Evaluation of Documents"
                            />
                          </div>
                          <div className="grid gap-2">
                            <ItemList
                              isComplete={reqsData.stage_summary['promissory_note_signing']}
                              description="Promissory Note Signing"
                            />
                            <ItemList
                              isComplete={reqsData.stage_summary['opening_of_account']}
                              description="Opening of LBP Account"
                            />
                          </div>
                        </div>
                      )}

                      <div className="mt-4 grid place-items-center">
                        <Button onClick={onViewDetails}>View Details</Button>
                      </div>
                    </AccordionContent>
                  </TimelineItem>
                </AccordionItem>
              );
            })}
        </Accordion>
      </Timeline>
    </div>
  );
}
