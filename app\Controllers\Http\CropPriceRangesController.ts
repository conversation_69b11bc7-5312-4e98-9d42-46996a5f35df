import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import AuditLog from 'App/Models/AuditLog'
import Crop, { CropSyncStatus } from 'App/Models/Crop'
import CropPrice from 'App/Models/CropPrice'
import CropPriceRange from 'App/Models/CropPriceRange'
import UpdateCropPriceRangeBacklogValidator from 'App/Validators/UpdateCropPriceRangeBacklogValidator'
import UpdateCropPriceRangeValidator from 'App/Validators/UpdateCropPriceRangeValidator'
import { startOfDay, endOfDay } from 'date-fns'
import { DateTime } from 'luxon'

export default class CropPriceRangesController {
  public async update({ auth, request, response }: HttpContextContract) {
    const { cropsPrices } = await request.validate(UpdateCropPriceRangeValidator)

    for (const cropsPrice of cropsPrices) {
      const checkPriceRangeToday = await CropPriceRange.query()
        .where('cropId', cropsPrice.cropId)
        .whereBetween('created_at', [
          startOfDay(DateTime.local({ zone: 'Asia/Manila' }).toJSDate()),
          endOfDay(DateTime.local({ zone: 'Asia/Manila' }).toJSDate()),
        ])
        .first()

      if (!checkPriceRangeToday) {
        await CropPriceRange.create(cropsPrice)
      } else {
        checkPriceRangeToday.lowPrice = cropsPrice.lowPrice
        checkPriceRangeToday.highPrice = cropsPrice.highPrice
        checkPriceRangeToday.lowBaptcPrice = cropsPrice.lowBaptcPrice
        checkPriceRangeToday.highBaptcPrice = cropsPrice.highBaptcPrice
        checkPriceRangeToday.lowNvatPrice = cropsPrice.lowNvatPrice
        checkPriceRangeToday.highNvatPrice = cropsPrice.highNvatPrice

        await checkPriceRangeToday.save()
      }

      // OLD CROP PRICE FOR TRADINGPOST
      const checkPriceToday = await CropPrice.query()
        .where('cropId', cropsPrice.cropId)
        .where('sellingPrice', cropsPrice.highPrice)
        .where('productionPrice', cropsPrice.highPrice)
        .whereBetween('created_at', [
          startOfDay(DateTime.local({ zone: 'Asia/Manila' }).toJSDate()),
          endOfDay(DateTime.local({ zone: 'Asia/Manila' }).toJSDate()),
        ])
        .first()

      if (!checkPriceToday) {
        await CropPrice.create({
          cropId: cropsPrice.cropId,
          sellingPrice: cropsPrice.highPrice,
          productionPrice: cropsPrice.highPrice,
        })
      }
    }

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE ENTRY (CROP PRICES RANGE)',
      model: 'CropPriceRange',
      data: JSON.stringify({ cropsPrices }),
    })

    return response.json({
      status: 1,
      message: 'Crops prices updated successfully',
    })
  }

  public async updateBacklog({ auth, request, response }: HttpContextContract) {
    const { cropsPrices } = await request.validate(UpdateCropPriceRangeBacklogValidator)

    for (const cropsPrice of cropsPrices) {
      if (cropsPrice.cropId === 0) {
        const createCrop = await Crop.firstOrCreate(
          {
            name: cropsPrice.name,
          },
          {
            name: cropsPrice.name,
            isSync: CropSyncStatus.NOT_SYNCED,
            keywords: cropsPrice.name,
          }
        )

        await CropPriceRange.create({
          lowPrice: cropsPrice.lowPrice,
          highPrice: cropsPrice.highPrice,
          lowBaptcPrice: cropsPrice.lowBaptcPrice,
          highBaptcPrice: cropsPrice.highBaptcPrice,
          lowNvatPrice: cropsPrice.lowNvatPrice,
          highNvatPrice: cropsPrice.highNvatPrice,
          cropId: createCrop.id,
          createdAt: DateTime.fromJSDate(
            cropsPrice.date.setZone('Asia/Manila').startOf('day').toJSDate()
          ),
          updatedAt: DateTime.fromJSDate(
            cropsPrice.date.setZone('Asia/Manila').startOf('day').toJSDate()
          ),
        })

        await CropPrice.create({
          cropId: createCrop.id,
          sellingPrice: cropsPrice.highPrice,
          productionPrice: cropsPrice.highPrice,
          createdAt: DateTime.fromJSDate(
            cropsPrice.date.setZone('Asia/Manila').startOf('day').toJSDate()
          ),
          updatedAt: DateTime.fromJSDate(
            cropsPrice.date.setZone('Asia/Manila').startOf('day').toJSDate()
          ),
        })
      } else {
        const checkPriceRangeToday = await CropPriceRange.query()
          .where('cropId', cropsPrice.cropId)
          .whereBetween('created_at', [
            cropsPrice.date.setZone('Asia/Manila').startOf('day').toJSDate(),
            cropsPrice.date.setZone('Asia/Manila').endOf('day').toJSDate(),
          ])
          .first()

        if (!checkPriceRangeToday) {
          await CropPriceRange.create({
            lowPrice: cropsPrice.lowPrice,
            highPrice: cropsPrice.highPrice,
            lowBaptcPrice: cropsPrice.lowBaptcPrice,
            highBaptcPrice: cropsPrice.highBaptcPrice,
            lowNvatPrice: cropsPrice.lowNvatPrice,
            highNvatPrice: cropsPrice.highNvatPrice,
            cropId: cropsPrice.cropId,
            createdAt: DateTime.fromJSDate(
              cropsPrice.date.setZone('Asia/Manila').startOf('day').toJSDate()
            ),
            updatedAt: DateTime.fromJSDate(
              cropsPrice.date.setZone('Asia/Manila').startOf('day').toJSDate()
            ),
          })
        } else {
          if (cropsPrice.lowPrice) {
            checkPriceRangeToday.lowPrice = cropsPrice.lowPrice
          }
          if (cropsPrice.highPrice) {
            checkPriceRangeToday.highPrice = cropsPrice.highPrice
          }
          if (cropsPrice.lowBaptcPrice) {
            checkPriceRangeToday.lowBaptcPrice = cropsPrice.lowBaptcPrice
          }
          if (cropsPrice.highBaptcPrice) {
            checkPriceRangeToday.highBaptcPrice = cropsPrice.highBaptcPrice
          }
          if (cropsPrice.lowNvatPrice) {
            checkPriceRangeToday.lowNvatPrice = cropsPrice.lowNvatPrice
          }
          if (cropsPrice.highNvatPrice) {
            checkPriceRangeToday.highNvatPrice = cropsPrice.highNvatPrice
          }

          await checkPriceRangeToday.save()
        }

        // OLD CROP PRICE FOR TRADINGPOST
        const checkPriceToday = await CropPrice.query()
          .where('cropId', cropsPrice.cropId)
          .where('sellingPrice', cropsPrice.highPrice)
          .where('productionPrice', cropsPrice.highPrice)
          .whereBetween('created_at', [
            cropsPrice.date.setZone('Asia/Manila').startOf('day').toJSDate(),
            cropsPrice.date.setZone('Asia/Manila').endOf('day').toJSDate(),
          ])
          .first()

        if (!checkPriceToday) {
          await CropPrice.create({
            cropId: cropsPrice.cropId,
            sellingPrice: cropsPrice.highPrice,
            productionPrice: cropsPrice.highPrice,
            createdAt: DateTime.fromJSDate(
              cropsPrice.date.setZone('Asia/Manila').startOf('day').toJSDate()
            ),
            updatedAt: DateTime.fromJSDate(
              cropsPrice.date.setZone('Asia/Manila').startOf('day').toJSDate()
            ),
          })
        }
      }
    }

    await AuditLog.create({
      userId: auth?.user?.id,
      action: 'UPDATE ENTRY (CROP PRICES RANGE BACKLOGS)',
      model: 'CropPriceRange',
    })

    return response.json({
      status: 1,
      message: 'Crops prices updated successfully',
    })
  }

  public async viewAll({ request, response }: HttpContextContract) {
    const { cropId } = request.params()
    const { page = 1, pageSize = 10 } = request.qs()

    const findCropsPrices = await CropPriceRange.query()
      .where('cropId', cropId)
      .preload('crop')
      .orderBy('updatedAt', 'desc')
      .paginate(page, pageSize)

    return response.json({
      status: 1,
      data: findCropsPrices,
    })
  }
}
