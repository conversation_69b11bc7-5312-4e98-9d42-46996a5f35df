/* eslint-disable @typescript-eslint/naming-convention */
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Bull from '@ioc:Rocketseat/Bull'
import { triggerCreditScoreComputation } from 'App/Helpers/CreditScoring'
import { initializeFarmerLoanRequirement } from 'App/Helpers/LoanRequirement'
import ProcessCropCloudSyncJob from 'App/Jobs/ProcessCropCloudSyncJob'
import ProcessCropPriceCloudSyncJob from 'App/Jobs/ProcessCropPriceCloudSyncJob'
import ProcessTradingPostCloudSyncJob from 'App/Jobs/ProcessTradingPostCloudSyncJob'
import ProcessUserCloudSyncJob from 'App/Jobs/ProcessUserCloudSyncJob'
import Crop, { CropSyncStatus } from 'App/Models/Crop'
import CropPrice, { CropPriceSyncStatus } from 'App/Models/CropPrice'
import Encoder from 'App/Models/Encoder'
import Farmer from 'App/Models/Farmer'
import FarmerCropPivot from 'App/Models/FarmerCropPivot'
import FarmerTransaction from 'App/Models/FarmerTransaction'
import FarmerTransactionHistory from 'App/Models/FarmerTransactionHistory'
import FarmerVehicle from 'App/Models/FarmerVehicle'
import LoanPeriodTracker, { LoanPeriodTrackerStatus } from 'App/Models/LoanPeriodTracker'
import TradingPostCrop from 'App/Models/TradingPostCrop'
import TradingPostLog, { LogStatus, TradingPostSyncStatus } from 'App/Models/TradingPostLog'
import User, { UserSyncStatus, UserType } from 'App/Models/User'
import KITA_CONFIG, { CONNECTION_MODE } from 'Config/kita'
import axios from 'axios'
import { startOfDay } from 'date-fns'
import { DateTime } from 'luxon'

export default class SyncDataController {
  // TRADINGPOST SYNCING
  public async syncSingleTradingPostReceiver({ request, response }: HttpContextContract) {
    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.CLOUD) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    const { secretKey, data } = request.body()

    if (KITA_CONFIG.CONNECTION_SECRETKEY !== secretKey) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid secret key',
      })
    }

    const tradingpostlogItem = data as any

    const checkReference = await TradingPostLog.findBy(
      'referenceNumber',
      tradingpostlogItem.reference_number
    )

    if (checkReference) {
      return response.json({
        status: 0,
        message: 'Already synced',
      })
    }

    const getFarmerDetails = tradingpostlogItem.farmer
    delete getFarmerDetails.id

    const getUserDetails = getFarmerDetails.user
    delete getUserDetails.id

    const checkUser = await User.firstOrCreate(
      { email: getFarmerDetails.user.email },
      {
        email: getUserDetails.email,
        username: getUserDetails.username,
        password: 'Farmerp@$$w0rd',
        userType: UserType.FARMER,
        isSync: UserSyncStatus.SYNCED,
        status: getUserDetails.status,
        rfidNumber: getUserDetails.rfid_number,
        createdAt: DateTime.fromISO(getUserDetails.created_at),
        updatedAt: DateTime.fromISO(getUserDetails.updated_at),
      }
    )

    const checkFarmer = await Farmer.firstOrCreate(
      { userId: checkUser.id },
      {
        userId: checkUser.id,
        firstName: getFarmerDetails.first_name,
        lastName: getFarmerDetails.last_name,
        qrCode: getFarmerDetails.qr_code,
        createdAt: DateTime.fromISO(tradingpostlogItem.created_at),
        updatedAt: DateTime.fromISO(tradingpostlogItem.updated_at),
      }
    )

    // NOTE: make sure that crops table is 1:1 replicated
    const cropsPlanted = tradingpostlogItem.crops?.map((item) => item.crop_id)

    const checkCropsPlanted = await FarmerCropPivot.query()
      .where('farmerId', checkFarmer.id)
      .whereIn('cropId', cropsPlanted)

    const checkMappedcropsPlanted = checkCropsPlanted.map((item) => item.cropId)

    const mappedCropsPlanted = cropsPlanted
      .filter((cropId) => !checkMappedcropsPlanted.includes(cropId))
      .map((cropId) => {
        return {
          farmerId: checkFarmer.id,
          cropId: cropId,
          createdAt: DateTime.fromISO(tradingpostlogItem.created_at),
          updatedAt: DateTime.fromISO(tradingpostlogItem.updated_at),
        }
      })

    await FarmerCropPivot.createMany(mappedCropsPlanted)

    await FarmerVehicle.firstOrCreate(
      {
        farmerId: checkFarmer.id,
        vehiclePlateNumber: tradingpostlogItem.vehicle_plate_number,
      },
      {
        farmerId: checkFarmer.id,
        vehiclePlateNumber: tradingpostlogItem.vehicle_plate_number,
        identifier: `${checkFarmer.id}-${tradingpostlogItem.vehicle_plate_number}`,
        createdAt: DateTime.fromISO(tradingpostlogItem.created_at),
        updatedAt: DateTime.fromISO(tradingpostlogItem.updated_at),
      }
    )

    const entryWeight = tradingpostlogItem.entry_weight
    const entryTime = tradingpostlogItem.entry_time
    const exitWeight = tradingpostlogItem.exit_weight
    const exitTime = tradingpostlogItem.exit_time

    const createTradingPostTransaction = await TradingPostLog.create({
      farmerId: checkFarmer.id,
      status: LogStatus.COMPLETE,
      referenceNumber: tradingpostlogItem.reference_number,
      vehiclePlateNumber: tradingpostlogItem.vehicle_plate_number,
      processedById: tradingpostlogItem.processed_by_id,
      isSync: TradingPostSyncStatus.SYNCED,
      entryTime,
      entryWeight,
      exitTime,
      exitWeight,
      createdAt: DateTime.fromISO(tradingpostlogItem.created_at),
      updatedAt: DateTime.fromISO(tradingpostlogItem.updated_at),
    })

    for (const tradingpostlogcropItem of tradingpostlogItem.crops) {
      const checkCropPrice = await CropPrice.query()
        .where('cropId', tradingpostlogcropItem.crop_id)
        .whereBetween('createdAt', [startOfDay(new Date(entryTime)), new Date(entryTime)])
        .orderBy('id', 'desc')
        .first()

      const checkCurrentPrice = await CropPrice.query()
        .where('cropId', tradingpostlogcropItem.crop_id)
        .orderBy('id', 'desc')
        .first()

      console.log('checkCropPrice: ', checkCropPrice)

      await TradingPostCrop.updateOrCreate(
        {
          tradingPostLogId: createTradingPostTransaction.id,
          cropId: tradingpostlogcropItem.crop_id,
        },
        {
          tradingPostLogId: createTradingPostTransaction.id,
          cropId: tradingpostlogcropItem.crop_id,
          percentage: tradingpostlogcropItem.percentage,
          sellingPrice: checkCropPrice
            ? checkCropPrice?.sellingPrice
            : checkCurrentPrice?.sellingPrice || 0,
          productionPrice: checkCropPrice
            ? checkCropPrice?.productionPrice
            : checkCurrentPrice?.productionPrice || 0,
          createdAt: DateTime.fromISO(tradingpostlogcropItem.created_at),
          updatedAt: DateTime.fromISO(tradingpostlogcropItem.updated_at),
        }
      )
    }

    // UPDATE FARMER TRANSACTION
    if (createTradingPostTransaction.status === LogStatus.COMPLETE) {
      // UPDATE FARMER TRANSACTION ON CLOUD
      if (KITA_CONFIG.CONNECTION_MODE === CONNECTION_MODE.CLOUD) {
        const tradingpostLog = await TradingPostLog.query()
          .preload('farmer', (subQuery) => {
            subQuery.preload('farmerInfo')
          })
          .preload('crops', (subQuery) => {
            subQuery.preload('crop')
          })
          .where('id', createTradingPostTransaction.id)
          .first()

        if (tradingpostLog) {
          const loanPeriodTracker = await LoanPeriodTracker.query()
            .where('userId', tradingpostLog.farmer.userId)
            .where('status', LoanPeriodTrackerStatus.ONGOING)
            .first()

          const findFarmerTransaction = await FarmerTransaction.firstOrCreate(
            {
              farmerId: tradingpostLog.farmerId,
            },
            {
              farmerId: tradingpostLog.farmerId,
              loanCycle: 1,
              beforeLoanStartAt: tradingpostLog.farmer.createdAt,
              tradingPostTotal: 0,
              tradingPostBeforeLoanTotal: 0,
              tradingPostAfterLoanTotal: 0,
              tradingPostDuringLoanTotal: 0,
              marketplaceTotal: 0,
              marketplaceAfterLoanTotal: 0,
              marketplaceBeforeLoanTotal: 0,
              marketplaceDuringLoanTotal: 0,
              salesTotal: 0,
              salesAfterLoanTotal: 0,
              salesBeforeLoanTotal: 0,
              salesDuringLoanTotal: 0,
            }
          )

          if (!loanPeriodTracker) {
            let subTotal = 0

            for (const crops of tradingpostLog.crops) {
              subTotal +=
                (tradingpostLog.entryWeight - tradingpostLog.exitWeight) *
                crops.percentage *
                (crops.sellingPrice || 0)
            }

            findFarmerTransaction.tradingPostTotal += subTotal
            findFarmerTransaction.tradingPostBeforeLoanTotal += subTotal

            await findFarmerTransaction.save()
          } else {
            let subTotal = 0

            for (const crops of tradingpostLog.crops) {
              subTotal +=
                (tradingpostLog.entryWeight - tradingpostLog.exitWeight) *
                crops.percentage *
                (crops.sellingPrice || 0)
            }

            findFarmerTransaction.tradingPostTotal += subTotal
            findFarmerTransaction.tradingPostUpdatedAt = tradingpostLog.createdAt

            if (
              tradingpostLog.createdAt.toMillis() >=
                loanPeriodTracker.beforeLoanStartAt.toMillis() &&
              tradingpostLog.createdAt.toMillis() <= loanPeriodTracker.beforeLoanEndAt.toMillis()
            ) {
              findFarmerTransaction.tradingPostBeforeLoanTotal += subTotal
            } else if (
              tradingpostLog.createdAt.toMillis() >=
                loanPeriodTracker.duringLoanStartAt.toMillis() &&
              tradingpostLog.createdAt.toMillis() <= loanPeriodTracker.duringLoanEndAt.toMillis()
            ) {
              findFarmerTransaction.tradingPostDuringLoanTotal += subTotal
            } else {
              findFarmerTransaction.tradingPostAfterLoanTotal += subTotal
            }

            await findFarmerTransaction.save()
          }

          await FarmerTransactionHistory.updateOrCreate(
            {
              farmerId: findFarmerTransaction.farmerId,
              loanCycle: findFarmerTransaction.loanCycle,
            },
            {
              farmerId: findFarmerTransaction.farmerId,
              loanCycle: findFarmerTransaction.loanCycle,
              beforeLoanStartAt: findFarmerTransaction.beforeLoanStartAt,
              tradingPostTotal: findFarmerTransaction.tradingPostTotal,
              tradingPostBeforeLoanTotal: findFarmerTransaction.tradingPostBeforeLoanTotal,
              tradingPostDuringLoanTotal: findFarmerTransaction.tradingPostDuringLoanTotal,
              tradingPostAfterLoanTotal: findFarmerTransaction.tradingPostAfterLoanTotal,
              tradingPostUpdatedAt: findFarmerTransaction.tradingPostUpdatedAt,
              salesTotal: findFarmerTransaction.salesTotal,
              salesBeforeLoanTotal: findFarmerTransaction.salesBeforeLoanTotal,
              salesDuringLoanTotal: findFarmerTransaction.salesDuringLoanTotal,
              salesAfterLoanTotal: findFarmerTransaction.salesAfterLoanTotal,
              salesUpdatedAt: findFarmerTransaction.salesUpdatedAt,
              marketplaceTotal: findFarmerTransaction.marketplaceTotal,
              marketplaceBeforeLoanTotal: findFarmerTransaction.marketplaceBeforeLoanTotal,
              marketplaceDuringLoanTotal: findFarmerTransaction.marketplaceDuringLoanTotal,
              marketplaceAfterLoanTotal: findFarmerTransaction.marketplaceAfterLoanTotal,
              marketplaceUpdatedAt: findFarmerTransaction.marketplaceUpdatedAt,
            }
          )
        }
      }

      await triggerCreditScoreComputation(checkFarmer.userId)
    }

    if (checkFarmer.$isLocal) {
      await initializeFarmerLoanRequirement(checkFarmer.id)
    }

    return response.json({
      status: 1,
      data: tradingpostlogItem.id,
      message: `TradingPost Log sync successfully`,
    })
  }

  public async syncTradingPostReceiver({ request, response }: HttpContextContract) {
    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.CLOUD) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    const { secretKey, data } = request.body()

    if (KITA_CONFIG.CONNECTION_SECRETKEY !== secretKey) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid secret key',
      })
    }

    const _tradingpostlogData = data as any[]

    const syncIds = [] as number[]

    for (const tradingpostlogItem of _tradingpostlogData) {
      syncIds.push(tradingpostlogItem.id)

      const checkReference = await TradingPostLog.findBy(
        'referenceNumber',
        tradingpostlogItem.reference_number
      )

      if (checkReference) continue // if reference exist, continue

      const getFarmerDetails = tradingpostlogItem.farmer
      delete getFarmerDetails.id

      const getUserDetails = getFarmerDetails.user
      delete getUserDetails.id

      const checkUser = await User.firstOrCreate(
        { email: getFarmerDetails.user.email },
        {
          email: getUserDetails.email,
          username: getUserDetails.username,
          password: 'Farmerp@$$w0rd',
          userType: UserType.FARMER,
          isSync: UserSyncStatus.SYNCED,
          status: getUserDetails.status,
          rfidNumber: getUserDetails.rfid_number,
          createdAt: DateTime.fromISO(getUserDetails.created_at),
          updatedAt: DateTime.fromISO(getUserDetails.updated_at),
        }
      )

      const checkFarmer = await Farmer.firstOrCreate(
        { userId: checkUser.id },
        {
          userId: checkUser.id,
          firstName: getFarmerDetails.first_name,
          lastName: getFarmerDetails.last_name,
          qrCode: getFarmerDetails.qr_code,
          createdAt: DateTime.fromISO(tradingpostlogItem.created_at),
          updatedAt: DateTime.fromISO(tradingpostlogItem.updated_at),
        }
      )

      // NOTE: make sure that crops table is 1:1 replicated
      const cropsPlanted = tradingpostlogItem.crops?.map((item) => item.crop_id)

      const checkCropsPlanted = await FarmerCropPivot.query()
        .where('farmerId', checkFarmer.id)
        .whereIn('cropId', cropsPlanted)

      const checkMappedcropsPlanted = checkCropsPlanted.map((item) => item.cropId)

      const mappedCropsPlanted = cropsPlanted
        .filter((cropId) => !checkMappedcropsPlanted.includes(cropId))
        .map((cropId) => {
          return {
            farmerId: checkFarmer.id,
            cropId: cropId,
            createdAt: DateTime.fromISO(tradingpostlogItem.created_at),
            updatedAt: DateTime.fromISO(tradingpostlogItem.updated_at),
          }
        })

      await FarmerCropPivot.createMany(mappedCropsPlanted)

      await FarmerVehicle.firstOrCreate(
        {
          farmerId: checkFarmer.id,
          vehiclePlateNumber: tradingpostlogItem.vehicle_plate_number,
        },
        {
          farmerId: checkFarmer.id,
          vehiclePlateNumber: tradingpostlogItem.vehicle_plate_number,
          identifier: `${checkFarmer.id}-${tradingpostlogItem.vehicle_plate_number}`,
          createdAt: DateTime.fromISO(tradingpostlogItem.created_at),
          updatedAt: DateTime.fromISO(tradingpostlogItem.updated_at),
        }
      )

      const entryWeight = tradingpostlogItem.entry_weight
      const entryTime = tradingpostlogItem.entry_time
      const exitWeight = tradingpostlogItem.exit_weight
      const exitTime = tradingpostlogItem.exit_time

      const createTradingPostTransaction = await TradingPostLog.create({
        farmerId: checkFarmer.id,
        status: LogStatus.COMPLETE,
        referenceNumber: tradingpostlogItem.reference_number,
        vehiclePlateNumber: tradingpostlogItem.vehicle_plate_number,
        processedById: tradingpostlogItem.processed_by_id,
        isSync: TradingPostSyncStatus.SYNCED,
        entryTime,
        entryWeight,
        exitTime,
        exitWeight,
        createdAt: DateTime.fromISO(tradingpostlogItem.created_at),
        updatedAt: DateTime.fromISO(tradingpostlogItem.updated_at),
      })

      for (const tradingpostlogcropItem of tradingpostlogItem.crops) {
        const checkCropPrice = await CropPrice.query()
          .where('cropId', tradingpostlogcropItem.crop_id)
          .whereBetween('createdAt', [startOfDay(new Date(entryTime)), new Date(entryTime)])
          .orderBy('id', 'desc')
          .first()

        const checkCurrentPrice = await CropPrice.query()
          .where('cropId', tradingpostlogcropItem.crop_id)
          .orderBy('id', 'desc')
          .first()

        console.log('checkCropPrice: ', checkCropPrice)

        await TradingPostCrop.updateOrCreate(
          {
            tradingPostLogId: createTradingPostTransaction.id,
            cropId: tradingpostlogcropItem.crop_id,
          },
          {
            tradingPostLogId: createTradingPostTransaction.id,
            cropId: tradingpostlogcropItem.crop_id,
            percentage: tradingpostlogcropItem.percentage,
            sellingPrice: checkCropPrice
              ? checkCropPrice?.sellingPrice
              : checkCurrentPrice?.sellingPrice || 0,
            productionPrice: checkCropPrice
              ? checkCropPrice?.productionPrice
              : checkCurrentPrice?.productionPrice || 0,
            createdAt: DateTime.fromISO(tradingpostlogcropItem.created_at),
            updatedAt: DateTime.fromISO(tradingpostlogcropItem.updated_at),
          }
        )
      }

      // UPDATE FARMER TRANSACTION
      if (createTradingPostTransaction.status === LogStatus.COMPLETE) {
        // UPDATE FARMER TRANSACTION ON CLOUD
        if (KITA_CONFIG.CONNECTION_MODE === CONNECTION_MODE.CLOUD) {
          const tradingpostLog = await TradingPostLog.query()
            .preload('farmer', (subQuery) => {
              subQuery.preload('farmerInfo')
            })
            .preload('crops', (subQuery) => {
              subQuery.preload('crop')
            })
            .where('id', createTradingPostTransaction.id)
            .first()

          if (tradingpostLog) {
            const loanPeriodTracker = await LoanPeriodTracker.query()
              .where('userId', tradingpostLog.farmer.userId)
              .where('status', LoanPeriodTrackerStatus.ONGOING)
              .first()

            const findFarmerTransaction = await FarmerTransaction.firstOrCreate(
              {
                farmerId: tradingpostLog.farmerId,
              },
              {
                farmerId: tradingpostLog.farmerId,
                loanCycle: 1,
                beforeLoanStartAt: tradingpostLog.farmer.createdAt,
                tradingPostTotal: 0,
                tradingPostBeforeLoanTotal: 0,
                tradingPostAfterLoanTotal: 0,
                tradingPostDuringLoanTotal: 0,
                marketplaceTotal: 0,
                marketplaceAfterLoanTotal: 0,
                marketplaceBeforeLoanTotal: 0,
                marketplaceDuringLoanTotal: 0,
                salesTotal: 0,
                salesAfterLoanTotal: 0,
                salesBeforeLoanTotal: 0,
                salesDuringLoanTotal: 0,
              }
            )

            if (!loanPeriodTracker) {
              let subTotal = 0

              for (const crops of tradingpostLog.crops) {
                subTotal +=
                  (tradingpostLog.entryWeight - tradingpostLog.exitWeight) *
                  crops.percentage *
                  (crops.sellingPrice || 0)
              }

              findFarmerTransaction.tradingPostTotal += subTotal
              findFarmerTransaction.tradingPostBeforeLoanTotal += subTotal

              await findFarmerTransaction.save()
            } else {
              let subTotal = 0

              for (const crops of tradingpostLog.crops) {
                subTotal +=
                  (tradingpostLog.entryWeight - tradingpostLog.exitWeight) *
                  crops.percentage *
                  (crops.sellingPrice || 0)
              }

              findFarmerTransaction.tradingPostTotal += subTotal
              findFarmerTransaction.tradingPostUpdatedAt = tradingpostLog.createdAt

              if (
                tradingpostLog.createdAt.toMillis() >=
                  loanPeriodTracker.beforeLoanStartAt.toMillis() &&
                tradingpostLog.createdAt.toMillis() <= loanPeriodTracker.beforeLoanEndAt.toMillis()
              ) {
                findFarmerTransaction.tradingPostBeforeLoanTotal += subTotal
              } else if (
                tradingpostLog.createdAt.toMillis() >=
                  loanPeriodTracker.duringLoanStartAt.toMillis() &&
                tradingpostLog.createdAt.toMillis() <= loanPeriodTracker.duringLoanEndAt.toMillis()
              ) {
                findFarmerTransaction.tradingPostDuringLoanTotal += subTotal
              } else {
                findFarmerTransaction.tradingPostAfterLoanTotal += subTotal
              }

              await findFarmerTransaction.save()
            }

            await FarmerTransactionHistory.updateOrCreate(
              {
                farmerId: findFarmerTransaction.farmerId,
                loanCycle: findFarmerTransaction.loanCycle,
              },
              {
                farmerId: findFarmerTransaction.farmerId,
                loanCycle: findFarmerTransaction.loanCycle,
                beforeLoanStartAt: findFarmerTransaction.beforeLoanStartAt,
                tradingPostTotal: findFarmerTransaction.tradingPostTotal,
                tradingPostBeforeLoanTotal: findFarmerTransaction.tradingPostBeforeLoanTotal,
                tradingPostDuringLoanTotal: findFarmerTransaction.tradingPostDuringLoanTotal,
                tradingPostAfterLoanTotal: findFarmerTransaction.tradingPostAfterLoanTotal,
                tradingPostUpdatedAt: findFarmerTransaction.tradingPostUpdatedAt,
                salesTotal: findFarmerTransaction.salesTotal,
                salesBeforeLoanTotal: findFarmerTransaction.salesBeforeLoanTotal,
                salesDuringLoanTotal: findFarmerTransaction.salesDuringLoanTotal,
                salesAfterLoanTotal: findFarmerTransaction.salesAfterLoanTotal,
                salesUpdatedAt: findFarmerTransaction.salesUpdatedAt,
                marketplaceTotal: findFarmerTransaction.marketplaceTotal,
                marketplaceBeforeLoanTotal: findFarmerTransaction.marketplaceBeforeLoanTotal,
                marketplaceDuringLoanTotal: findFarmerTransaction.marketplaceDuringLoanTotal,
                marketplaceAfterLoanTotal: findFarmerTransaction.marketplaceAfterLoanTotal,
                marketplaceUpdatedAt: findFarmerTransaction.marketplaceUpdatedAt,
              }
            )
          }
        }
      }

      if (checkFarmer.$isLocal) {
        await initializeFarmerLoanRequirement(checkFarmer.id)
      }
    }

    return response.json({
      status: 1,
      data: { syncIds },
      message: `${syncIds.length} TradingPost Logs sync successfully`,
    })
  }

  public async syncTradingPostSender({ auth, response }: HttpContextContract) {
    const { user } = auth

    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.LOCAL) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid user',
      })
    }

    const checkLastSync = await TradingPostLog.query()
      .where('status', LogStatus.COMPLETE)
      .where('isSync', TradingPostSyncStatus.NOT_SYNCED)
      .orderBy('id', 'asc')
      .first()

    if (!checkLastSync) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Tradingpost data is upto date!',
      })
    }

    // limit to 20
    const fetchNotSyncTradingPostLog = await TradingPostLog.query()
      .preload('farmer', (subQuery) => {
        subQuery.preload('user').preload('farmerVehicles')
      })
      .preload('processedBy')
      .preload('crops', (subQuery) => {
        subQuery.preload('crop')
      })
      .where('status', LogStatus.COMPLETE)
      .where('isSync', TradingPostSyncStatus.NOT_SYNCED)
      .limit(20)

    const dataPayload = {
      secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
      data: fetchNotSyncTradingPostLog,
    }

    // Call cloud api
    await axios
      .post(`${KITA_CONFIG.API_URL}/sync/receiver/tradingpost`, dataPayload)
      .then(async (response) => {
        const _response = response.data
        console.log('_response: ', _response)

        // should return all sync ids
        const updateToSync = await TradingPostLog.query()
          .whereIn('id', _response.data?.syncIds)
          .update('isSync', TradingPostSyncStatus.SYNCED)

        console.log('updateToSync: ', updateToSync)
      })
      .catch((e) => {
        console.error(e)
      })

    return response.json({
      status: 1,
      message: 'Trading post batch synced!',
    })
  }

  // GENERAL SYNC STATUS
  public async getModelSyncStatusReceiver({ request, response }: HttpContextContract) {
    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.CLOUD) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    const { secretKey, data } = request.body()

    if (KITA_CONFIG.CONNECTION_SECRETKEY !== secretKey) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid secret key',
      })
    }

    if (data === 'User') {
      const checkNotSyncedUsers = await User.query()
        .where('isSync', UserSyncStatus.NOT_SYNCED)
        .whereIn('userType', [UserType.FARMER, UserType.ENCODER_1, UserType.ENCODER_2])

      return response.json({
        status: 1,
        model: 'User',
        data: checkNotSyncedUsers.length,
      })
    }

    if (data === 'Crop') {
      const checkNotSyncedCrops = await Crop.query().where('isSync', CropSyncStatus.NOT_SYNCED)

      return response.json({
        status: 1,
        model: 'Crop',
        data: checkNotSyncedCrops.length,
      })
    }

    if (data === 'CropPrice') {
      const checkNotSyncedCropPrices = await CropPrice.query().where(
        'isSync',
        CropPriceSyncStatus.NOT_SYNCED
      )

      return response.json({
        status: 1,
        model: 'CropPrice',
        data: checkNotSyncedCropPrices.length,
      })
    }

    /**
     * 1. get model name from request body
     * 2. query not sync item from model
     * 3. return length of queried item, model name
     */
    response.status(400)
    return response.json({
      status: 0,
      data: 'Invalid Model Name',
    })
  }

  public async getModelSyncStatusSender({ auth, request, response }: HttpContextContract) {
    const { user } = auth

    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.LOCAL) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid user',
      })
    }

    const { model } = request.body()

    // Call cloud api
    try {
      if (model === 'TradingPostLog') {
        const checkNotSyncedUsers = await TradingPostLog.query()
          .where('status', LogStatus.COMPLETE)
          .where('isSync', TradingPostSyncStatus.NOT_SYNCED)

        return response.json({
          status: 1,
          model: 'TradingPostLog',
          data: checkNotSyncedUsers.length,
        })
      }

      const _res = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/model/status`, {
          data: model,
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
        })
        .then(async (response) => response.data)

      return response.json({
        status: 1,
        model: _res.model,
        data: _res.data,
      })
    } catch (e) {
      console.error(e)

      response.status(400)
      return response.json({
        status: 0,
        message: e?.response?.data?.message || e.message,
      })
    }

    /**
     * 1. call api request on receiver (send model name as body), if has internet return number of not synced model item, return not internet connection
     * 2. return model name
     */
  }

  // USER SYNCING
  public async syncUsersValidateReceiver({ request, response }: HttpContextContract) {
    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.CLOUD) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    const { secretKey, syncIds } = request.body()

    if (KITA_CONFIG.CONNECTION_SECRETKEY !== secretKey) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid secret key',
      })
    }

    const checkUserUpdateSync = await User.query()
      .whereIn('id', syncIds)
      .update('isSync', UserSyncStatus.SYNCED)

    console.log('checkUserUpdateSync: ', checkUserUpdateSync)

    return response.json({
      status: 1,
      data: 'Users batch synced!',
    })
  }

  public async syncUsersReceiver({ request, response }: HttpContextContract) {
    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.CLOUD) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    const { secretKey } = request.body()

    if (KITA_CONFIG.CONNECTION_SECRETKEY !== secretKey) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid secret key',
      })
    }

    // const checkNotSyncedCrops = await Crop.query().where('isSync', CropSyncStatus.NOT_SYNCED)

    // if (checkNotSyncedCrops.length > 0) {
    //   response.status(400)
    //   return response.json({
    //     status: 0,
    //     message: 'Sync crops list first!',
    //   })
    // }

    const checkNotSyncedUsers = await User.query()
      .preload('farmer', (subQuery) => {
        subQuery
          .select('id', 'user_id', 'first_name', 'last_name', 'qr_code', 'created_at', 'updated_at')
          .preload('farmerVehicles')
          .preload('cropsPlanted', (subQuery) => {
            subQuery.preload('crop')
          })
      })
      .preload('encoder')
      .where('isSync', UserSyncStatus.NOT_SYNCED)
      .whereIn('userType', [UserType.FARMER, UserType.ENCODER_1, UserType.ENCODER_2])

    if (checkNotSyncedUsers.length === 0) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Users list is upto date!',
      })
    }

    return response.json({
      status: 1,
      data: checkNotSyncedUsers,
    })
  }

  public async syncUsersSender({ auth, response }: HttpContextContract) {
    const { user } = auth

    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.LOCAL) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid user',
      })
    }

    /**
     * 1. call api request on receiver (syncUsersReceiver)
     * 2. get data from cloud
     * 3. loop user arrays, create farmer/encoder
     * 4. mark as sync
     * 5. return ids sync back to cloud (syncUsersValidateReceiver)
     */

    // Call cloud api
    try {
      const _users = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/user`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
        })
        .then(async (response) => response.data)

      const syncIds = [] as number[]

      for (const _user of _users.data) {
        syncIds.push(_user.id)

        const getFarmerDetails = _user?.farmer

        const getEncoderDetails = _user?.encoder

        const checkUser = await User.firstOrCreate(
          { email: _user.email },
          {
            email: _user.email,
            username: _user.username,
            password: 'p@$$w0rd',
            userType: _user.user_type,
            isSync: UserSyncStatus.SYNCED,
            status: _user.status,
            createdAt: DateTime.fromISO(_user.created_at),
            updatedAt: DateTime.fromISO(_user.updated_at),
            rfidNumber: _user.rfid_number,
          }
        )

        if (checkUser.userType === UserType.FARMER) {
          const checkFarmer = await Farmer.updateOrCreate(
            { userId: checkUser.id },
            {
              userId: checkUser.id,
              firstName: getFarmerDetails.first_name,
              lastName: getFarmerDetails.last_name,
              qrCode: getFarmerDetails.qr_code,
              createdAt: DateTime.fromISO(getFarmerDetails.created_at),
              updatedAt: DateTime.fromISO(getFarmerDetails.updated_at),
            }
          )

          const mappedFarmerVehicle = getFarmerDetails.farmerVehicles?.map((item) => ({
            farmerId: checkFarmer.id,
            vehiclePlateNumber: item.vehicle_plate_number,
            identifier: `${checkFarmer.id}-${item.vehicle_plate_number}`,
            createdAt: DateTime.fromISO(item.created_at),
            updatedAt: DateTime.fromISO(item.updated_at),
          }))

          if (mappedFarmerVehicle.length > 0) {
            await FarmerVehicle.updateOrCreateMany('identifier', mappedFarmerVehicle)
          }

          const cropsPlantedPivot = getFarmerDetails.cropsPlanted?.map((item) => ({
            cropId: item.crop_id,
            farmerId: checkFarmer.id,
            createdAt: DateTime.fromISO(_user.created_at),
            updatedAt: DateTime.fromISO(_user.updated_at),
          }))

          if (cropsPlantedPivot.length > 0) {
            await FarmerCropPivot.createMany(cropsPlantedPivot)
          }
        }

        if (
          checkUser.userType === UserType.ENCODER_1 ||
          checkUser.userType === UserType.ENCODER_2
        ) {
          await Encoder.firstOrCreate(
            {
              userId: checkUser.id,
            },
            {
              userId: checkUser.id,
              firstName: getEncoderDetails.first_name,
              lastName: getEncoderDetails.last_name,
              createdAt: DateTime.fromISO(getEncoderDetails.created_at),
              updatedAt: DateTime.fromISO(getEncoderDetails.updated_at),
            }
          )
        }
      }

      const _validateResponse = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/user/validate`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
          syncIds,
        })
        .then(async (response) => response.data)

      console.log(_validateResponse.message)
    } catch (e) {
      console.error(e)

      response.status(400)
      return response.json({
        status: 0,
        message: e?.response?.data?.message || e.message,
      })
    }

    return response.json({
      status: 1,
      data: 'Users batch synced!',
    })
  }

  // CROP SYNCING
  public async syncCropValidateReceiver({ request, response }: HttpContextContract) {
    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.CLOUD) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    const { secretKey, syncIds } = request.body()

    if (KITA_CONFIG.CONNECTION_SECRETKEY !== secretKey) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid secret key',
      })
    }

    const checkCropUpdateSync = await Crop.query()
      .whereIn('id', syncIds)
      .update('isSync', CropSyncStatus.SYNCED)

    console.log('checkCropUpdateSync: ', checkCropUpdateSync)

    return response.json({
      status: 1,
      data: 'Crops batch synced!',
    })
  }

  public async syncCropReceiver({ request, response }: HttpContextContract) {
    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.CLOUD) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    const { secretKey } = request.body()

    if (KITA_CONFIG.CONNECTION_SECRETKEY !== secretKey) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid secret key',
      })
    }

    const checkNotSyncedCrops = await Crop.query()
      .where('isSync', CropSyncStatus.NOT_SYNCED)
      .orderBy('id', 'asc')

    if (checkNotSyncedCrops.length === 0) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Crops list is upto date!',
      })
    }

    return response.json({
      status: 1,
      data: checkNotSyncedCrops,
    })
  }

  public async syncCropSender({ auth, response }: HttpContextContract) {
    const { user } = auth

    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.LOCAL) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid user',
      })
    }

    try {
      const _crops = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/crop`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
        })
        .then(async (response) => response.data)

      const syncIds = [] as number[]

      for (const _crop of _crops.data) {
        syncIds.push(_crop.id)

        await Crop.updateOrCreate(
          { id: _crop.id },
          {
            id: _crop.id,
            name: _crop.name,
            harvestDays: _crop.harvest_days,
            isSync: CropSyncStatus.SYNCED,
            status: _crop.status,
            createdAt: DateTime.fromISO(_crop.created_at),
            updatedAt: DateTime.fromISO(_crop.updated_at),
          }
        )
      }

      const _validateResponse = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/crop/validate`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
          syncIds,
        })
        .then(async (response) => response.data)

      console.log(_validateResponse.message)
    } catch (e) {
      console.error(e)

      response.status(400)
      return response.json({
        status: 0,
        message: e?.response?.data?.message || e.message,
      })
    }

    return response.json({
      status: 1,
      data: 'Crops batch synced!',
    })
  }

  // CROP PRICE SYNCING
  public async syncCropPriceValidateReceiver({ request, response }: HttpContextContract) {
    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.CLOUD) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    const { secretKey, syncIds } = request.body()

    if (KITA_CONFIG.CONNECTION_SECRETKEY !== secretKey) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid secret key',
      })
    }

    const checkCropPriceUpdateSync = await CropPrice.query()
      .whereIn('id', syncIds)
      .update('isSync', CropPriceSyncStatus.SYNCED)

    console.log('checkCropPriceUpdateSync: ', checkCropPriceUpdateSync)

    return response.json({
      status: 1,
      data: 'CropPrices batch synced!',
    })
  }

  public async syncCropPriceReceiver({ request, response }: HttpContextContract) {
    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.CLOUD) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    const { secretKey } = request.body()

    if (KITA_CONFIG.CONNECTION_SECRETKEY !== secretKey) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid secret key',
      })
    }

    // const checkNotSyncedCrops = await Crop.query().where('isSync', CropSyncStatus.NOT_SYNCED)

    // if (checkNotSyncedCrops.length > 0) {
    //   response.status(400)
    //   return response.json({
    //     status: 0,
    //     message: 'Sync crops list first!',
    //   })
    // }

    const checkNotSyncedCropPrices = await CropPrice.query()
      .where('isSync', CropPriceSyncStatus.NOT_SYNCED)
      .orderBy('id', 'asc')

    if (checkNotSyncedCropPrices.length === 0) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'CropPrices list is upto date!',
      })
    }

    return response.json({
      status: 1,
      data: checkNotSyncedCropPrices,
    })
  }

  public async syncCropPriceSender({ auth, response }: HttpContextContract) {
    const { user } = auth

    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.LOCAL) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid user',
      })
    }

    try {
      const _cropPrices = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/crop/price`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
        })
        .then(async (response) => response.data)

      const syncIds = [] as number[]

      for (const _cropPrice of _cropPrices.data) {
        syncIds.push(_cropPrice.id)

        await CropPrice.updateOrCreate(
          { id: _cropPrice.id },
          {
            id: _cropPrice.id,
            cropId: _cropPrice.crop_id,
            sellingPrice: _cropPrice.selling_price || 0,
            productionPrice: _cropPrice.production_price || 0,
            isSync: CropPriceSyncStatus.SYNCED,
            createdAt: DateTime.fromISO(_cropPrice.created_at),
            updatedAt: DateTime.fromISO(_cropPrice.updated_at),
          }
        )
      }

      const _validateResponse = await axios
        .post(`${KITA_CONFIG.API_URL}/sync/receiver/crop/price/validate`, {
          secretKey: KITA_CONFIG.CONNECTION_SECRETKEY,
          syncIds,
        })
        .then(async (response) => response.data)

      console.log(_validateResponse.message)
    } catch (e) {
      console.error(e)

      response.status(400)
      return response.json({
        status: 0,
        message: e?.response?.data?.message || e.message,
      })
    }

    return response.json({
      status: 1,
      data: 'CropPrices batch synced!',
    })
  }

  //////////////
  public async syncFromCloudSender({ auth, response }: HttpContextContract) {
    const { user } = auth

    if (KITA_CONFIG.CONNECTION_MODE !== CONNECTION_MODE.LOCAL) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid connection type',
      })
    }

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Invalid user',
      })
    }

    // SYNC CROP
    await Bull.add(
      new ProcessCropCloudSyncJob().key,
      {},
      {
        attempts: 5,
      }
    )

    await Bull.add(
      new ProcessUserCloudSyncJob().key,
      {},
      {
        attempts: 5,
      }
    )

    await Bull.add(
      new ProcessCropPriceCloudSyncJob().key,
      {},
      {
        attempts: 5,
      }
    )

    await Bull.add(
      new ProcessTradingPostCloudSyncJob().key,
      {},
      {
        attempts: 5,
      }
    )

    return response.json({
      status: 1,
      data: 'Syncing started!',
    })
  }
}
