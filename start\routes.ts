/*
|--------------------------------------------------------------------------
| Routes
|--------------------------------------------------------------------------
|
| This file is dedicated for defining HTTP routes. A single file is enough
| for majority of projects, however you can define routes in different
| files and just make sure to import them inside this file. For example
|
| Define routes in following two files
| ├── start/routes/cart.ts
| ├── start/routes/customer.ts
|
| and then import them inside `start/routes.ts` as follows
|
| import './routes/cart'
| import './routes/customer'
|
*/

import Route from '@ioc:Adonis/Core/Route'

Route.get('/', async () => {
  return 'KITA AI DEV API'
})

// AUTHENTICATION
Route.post('/register/farmer', 'AuthController.registerFarmer')
Route.post('/tradingpost/weight', 'ApisController.weight')
Route.post('/tradingpost/rfid', 'ApisController.rfid')
Route.post('/tradingpost/platenumber', 'ApisController.platenumber')
Route.post('/NotificationInfo/TollgateInfo', 'ApisController.platenumber_test')
Route.post('/NotificationInfo/KeepAlive', 'ApisController.keepalive')

Route.post('/login', 'AuthController.login')
Route.post('/logout', 'AuthController.logout')

Route.get('/crops/viewAll', 'CropsController.viewAllPublic')
Route.get('/creditscore/group/viewAll', 'CreditScoreGroupsController.viewAllPublic')
Route.get('/chemicals/viewAll', 'ChemicalsController.viewAllPublic')
Route.get('/chemicals/subcategory/viewAll', 'ChemicalSubcategoriesController.viewAllPublic')
Route.get(
  '/chemicals/activeingredient/viewAll',
  'ChemicalActiveIngredientsController.viewAllPublic'
)
Route.get('/chemicals/modeofaction/viewAll', 'ChemicalModeOfActionsController.viewAllPublic')
Route.get('/fertilizers/viewAll', 'FertilizersController.viewAllPublic')
Route.get('/otherproducts/viewAll', 'OtherProductsController.viewAllPublic')
Route.get('/seeds/viewAll', 'SeedsController.viewAllPublic')
Route.get('/seeds/subcategory/viewAll', 'SeedSubcategoriesController.viewAllPublic')
Route.get('/seeds/breed/viewAll', 'SeedBreedsController.viewAllPublic')
Route.get('/seeds/variety/viewAll', 'SeedVarietiesController.viewAllPublic')

// ADMIN ROUTES
Route.group(() => {
  // AUDIT LOGS
  Route.get('/auditlogs/viewAll', 'AuditLogsController.viewAll')

  // USERS
  Route.post('/user/create', 'UsersController.create')
  Route.post('/user/update', 'UsersController.update')
  Route.post('/user/activate', 'UsersController.activate')
  Route.post('/user/deactivate', 'UsersController.deactivate')
  Route.get('/user/viewAll', 'UsersController.viewAllByAdmin')
  Route.get('/user/view/:userId', 'UsersController.viewByIdByAdmin')
  Route.get('/user/farmers/viewAll', 'UsersController.viewAllFarmersByAdmin')
  Route.get('/user/farmers/viewAllPaginated', 'UsersController.viewAllFarmersByAdminPaginated')
  Route.get(
    '/user/farmers/members/viewAll',
    'FieldRelationOfficersController.viewAllRegisteredAccounts'
  )
  Route.post('/user/farmer/update', 'UsersController.updateFarmer')
  Route.get('/user/farmer/view/:userId', 'UsersController.viewFarmerByIdByAdmin')
  Route.post('/user/farmer/approve', 'UsersController.farmer_approve')
  Route.post('/user/farmer/reject', 'UsersController.farmer_reject')
  Route.post('/user/farmer/creditscore/update', 'UsersController.update_credit_score')
  Route.post('/user/farmer/qr/generate', 'UsersController.farmer_generate_qr')

  //FARMERS
  Route.group(() => {
    // FARMER LANDBANK REQUIREMENT
    Route.post('/landbank-requirement/update', 'FarmerLandbankRequirementsController.update')
    Route.post('/landbank-requirement/remove', 'FarmerLandbankRequirementsController.remove')

    // BULK IMPORT USER
    Route.post('/import', 'ImportUsersController.upload')
    Route.post('/import/validate', 'ImportUsersController.validate')
    Route.get('/imported/viewAll', 'ImportUsersController.viewAll')

    // LOAN REQUIREMENT
    Route.get(
      '/:farmerId/loanrequirement/view',
      'FarmerLoanRequirementsController.viewLoanRequirements'
    )
    Route.get(
      '/:farmerId/loanrequirement/stage/:stage/view',
      'FarmerLoanRequirementsController.viewLoanRequirementStage'
    )
    Route.get(
      '/:farmerId/loanrequirement/item/:farmerLoanRequirementItemId/logs',
      'FarmerLoanRequirementsController.viewLoanRequirementItemLogs'
    )
    Route.post(
      '/:farmerId/loanrequirement/item/update',
      'FarmerLoanRequirementsController.updateLoanRequirementItem'
    )
  }).prefix('/user/farmers')

  // NON LOAN HOLDER
  Route.get('/user/farmers/nonloanholder', 'MembersController.nonLoanHolderViewAll')
  Route.get('/user/farmers/nonloanholder/dashboard', 'MembersController.nonLoanHolderDashboard')
  // LOAN HOLDER
  Route.get('/user/farmers/loanholder', 'MembersController.loanHolderViewAll')
  Route.get('/user/farmers/loanholder/dashboard', 'MembersController.loanHolderDashboard')
  // LOAN APPLICANT
  Route.get('/user/farmers/loanapplicant', 'MembersController.loanApplicantHolderViewAll')
  Route.get(
    '/user/farmers/loanapplicant/dashboard',
    'MembersController.loanApplicantHolderDashboard'
  )
  Route.get(
    '/user/farmers/loanapplicant/:loanApplicationSubmissionId/view',
    'MembersController.loanApplicantHolderViewDetail'
  )
  Route.get(
    '/user/farmers/approved/loanapplicant',
    'MembersController.approvedLoanApplicantHolderUserViewAll'
  )
  // LOAN APPLICANT ACTIONS
  Route.post('/user/farmers/loanapplicant/approve', 'MembersController.approveLoanApplicant')
  Route.post('/user/farmers/loanapplicant/reject', 'MembersController.rejectLoanApplicant')

  // CROPS
  Route.post('/crops/create', 'CropsController.create')
  Route.post('/crops/create/single', 'CropsController.create_single')
  Route.post('/crops/update', 'CropsController.update')
  Route.get('/crops/viewAll', 'CropsController.viewAll')
  Route.get('/crops/viewAllPaginated', 'CropsController.viewAllPaginated')
  Route.post('/crops/prices/update', 'CropPricesController.update')
  Route.get('/crops/prices/history/:cropId', 'CropPricesController.viewAll')

  // CROP PRICE RANGE
  Route.post('/crops/price-ranges/update', 'CropPriceRangesController.update')
  Route.post('/crops/price-ranges/update/backlog', 'CropPriceRangesController.updateBacklog')
  Route.get('/crops/price-ranges/history/:cropId', 'CropPriceRangesController.viewAll')

  // CHEMICALS
  Route.post('/chemicals/create', 'ChemicalsController.create')
  Route.post('/chemicals/update', 'ChemicalsController.update')
  Route.get('/chemicals/viewAll', 'ChemicalsController.viewAllAdmin')
  Route.get('/chemicals/viewAllPaginated', 'ChemicalsController.viewAllPaginated')

  // CHEMICALS SUBCATEGORY
  Route.get('/chemicals/subcategory/viewAll', 'ChemicalSubcategoriesController.viewAllAdmin')
  Route.get(
    '/chemicals/subcategory/viewAllPaginated',
    'ChemicalSubcategoriesController.viewAllPaginated'
  )
  Route.post('/chemicals/subcategory/create', 'ChemicalSubcategoriesController.create')
  Route.post('/chemicals/subcategory/update', 'ChemicalSubcategoriesController.update')

  // CHEMICALS ACTIVE INGREDIENT
  Route.get(
    '/chemicals/activeingredient/viewAll',
    'ChemicalActiveIngredientsController.viewAllAdmin'
  )
  Route.get(
    '/chemicals/activeingredient/viewAllPaginated',
    'ChemicalActiveIngredientsController.viewAllPaginated'
  )
  Route.post('/chemicals/activeingredient/create', 'ChemicalActiveIngredientsController.create')
  Route.post('/chemicals/activeingredient/update', 'ChemicalActiveIngredientsController.update')

  // CHEMICALS MODE OF ACTION
  Route.get('/chemicals/modeofaction/viewAll', 'ChemicalModeOfActionsController.viewAllAdmin')
  Route.get(
    '/chemicals/modeofaction/viewAllPaginated',
    'ChemicalModeOfActionsController.viewAllPaginated'
  )
  Route.post('/chemicals/modeofaction/create', 'ChemicalModeOfActionsController.create')
  Route.post('/chemicals/modeofaction/update', 'ChemicalModeOfActionsController.update')

  // FERTILIZERS
  Route.post('/fertilizers/create', 'FertilizersController.create')
  Route.post('/fertilizers/update', 'FertilizersController.update')
  Route.get('/fertilizers/viewAll', 'FertilizersController.viewAllAdmin')
  Route.get('/fertilizers/viewAllPaginated', 'FertilizersController.viewAllPaginated')

  // OTHER PRODUCTS
  Route.post('/otherproducts/create', 'OtherProductsController.create')
  Route.post('/otherproducts/update', 'OtherProductsController.update')
  Route.get('/otherproducts/viewAll', 'OtherProductsController.viewAllAdmin')
  Route.get('/otherproducts/viewAllPaginated', 'OtherProductsController.viewAllPaginated')

  // SEED
  Route.post('/seeds/create', 'SeedsController.create')
  Route.post('/seeds/update', 'SeedsController.update')
  Route.get('/seeds/viewAll', 'SeedsController.viewAllAdmin')
  Route.get('/seeds/viewAllPaginated', 'SeedsController.viewAllPaginated')

  // SEED SUBCATEGORY/CROP TYPE
  Route.get('/seeds/subcategory/viewAll', 'SeedSubcategoriesController.viewAllAdmin')
  Route.get('/seeds/subcategory/viewAllPaginated', 'SeedSubcategoriesController.viewAllPaginated')
  Route.post('/seeds/subcategory/create', 'SeedSubcategoriesController.create')
  Route.post('/seeds/subcategory/update', 'SeedSubcategoriesController.update')

  // SEED BREED
  Route.get('/seeds/breed/viewAll', 'SeedBreedsController.viewAllAdmin')
  Route.get('/seeds/breed/viewAllPaginated', 'SeedBreedsController.viewAllPaginated')
  Route.post('/seeds/breed/create', 'SeedBreedsController.create')
  Route.post('/seeds/breed/update', 'SeedBreedsController.update')

  // SEED VARIETY
  Route.get('/seeds/variety/viewAll', 'SeedVarietiesController.viewAllAdmin')
  Route.get('/seeds/variety/viewAllPaginated', 'SeedVarietiesController.viewAllPaginated')
  Route.post('/seeds/variety/create', 'SeedVarietiesController.create')
  Route.post('/seeds/variety/update', 'SeedVarietiesController.update')

  // DASHBOARD
  Route.get(
    '/dashboard/viewTradingpostSalesSummary',
    'DashboardController.viewTradingpostSalesSummary'
  )
  Route.get(
    '/dashboard/viewMarketplaceSalesSummary',
    'DashboardController.viewMarketplaceSalesSummary'
  )
  Route.get('/dashboard/viewRegisteredAccounts', 'DashboardController.viewRegisteredAccounts')
  Route.get('/dashboard/viewTopTransactionValue', 'DashboardController.viewTopTransactionValue')
  Route.get(
    '/dashboard/viewTradingpostTopProducts',
    'DashboardController.viewTradingpostTopProducts'
  )
  Route.get(
    '/dashboard/viewMarketplaceTopProducts',
    'DashboardController.viewMarketplaceTopProducts'
  )
  Route.get('/dashboard/viewCropPricesTrend', 'DashboardController.viewCropPricesTrend')

  // TRADING POST
  Route.group(() => {
    Route.get('/viewAll', 'TradingPostLogsController.viewAll')
    Route.get('/transaction/view/:farmerId', 'TradingPostLogsController.viewAllByFarmerId')
    Route.post('/manual/entry', 'TradingPostLogsController.manual_entry')
    Route.post('/import/entry', 'TradingPostLogsController.imported_entry')
    Route.get('/history/view/:farmerUserId', 'TradingPostLogsController.viewAllByFarmerUserId')
  }).prefix('/tradingpost')

  // MARKETPLACE
  Route.group(() => {
    // PRODUCT
    Route.group(() => {
      Route.post('/create', 'MarketplaceProductsController.create')
      Route.post('/update', 'MarketplaceProductsController.update')
      Route.get('/viewAll', 'MarketplaceProductsController.viewAll')
      Route.get('/view/:marketplaceProductId', 'MarketplaceProductsController.viewById')
    }).prefix('/product')

    // ORDERS
    Route.group(() => {
      Route.post('/create', 'MarketplaceOrdersController.create')
      Route.post('/status/update', 'MarketplaceOrdersController.manageOrderStatus')
      Route.get('/dashboard', 'MarketplaceOrdersController.dashboard')
      Route.get('/viewAll', 'MarketplaceOrdersController.viewAll')
      Route.get('/view/:marketplaceOrderId', 'MarketplaceOrdersController.viewById')
      Route.get('/view/:marketplaceOrderId/soa', 'MarketplaceOrdersController.generateSOA')
      Route.get('/viewAll/soa', 'MarketplaceOrdersController.generateBulkSOA')
    }).prefix('/order')

    Route.get('/history/view/:farmerUserId', 'MarketplaceOrdersController.viewAllByFarmerUserId')
  }).prefix('/marketplace')

  // TOPUP
  Route.group(() => {
    Route.post('/request', 'TopupRequestsController.create')
    Route.post('/approve', 'TopupRequestsController.approve')
    Route.post('/reject', 'TopupRequestsController.reject')
    Route.get('/viewAll', 'TopupRequestsController.viewAllByAdmin')
    Route.get('/view/:topupRequestId', 'TopupRequestsController.viewAllById')
    Route.get('/history/view/:farmerUserId', 'TopupRequestsController.viewAllByFarmerUserId')
  }).prefix('/topup')

  // TOPDOWN
  Route.group(() => {
    Route.post('/request', 'TopdownRequestsController.create')
    Route.post('/approve', 'TopdownRequestsController.approve')
    Route.post('/reject', 'TopdownRequestsController.reject')
    Route.get('/viewAll', 'TopdownRequestsController.viewAllByAdmin')
    Route.get('/view/:topdownRequestId', 'TopdownRequestsController.viewAllById')
    Route.get('/history/view/:farmerUserId', 'TopdownRequestsController.viewAllByFarmerUserId')
  }).prefix('/topdown')

  // LOAN PAYMENT
  Route.group(() => {
    Route.post('/request', 'LoanPaymentRequestsController.create')
    Route.post('/approve', 'LoanPaymentRequestsController.approve')
    Route.post('/reject', 'LoanPaymentRequestsController.reject')
    Route.get('/viewAll', 'LoanPaymentRequestsController.viewAllByFinance')
    Route.get('/view/:loanPaymentRequestId', 'LoanPaymentRequestsController.viewAllById')
    Route.get('/history/view/:farmerUserId', 'LoanPaymentRequestsController.viewAllByFarmerUserId')
    Route.get('/dashboard', 'LoanPaymentRequestsController.dashboard')
  }).prefix('/loanpayment')

  // LOANAPPLICATIONSUBMISSION
  Route.group(() => {
    Route.post('/submit', 'LoanApplicationSubmissionsController.create')
    Route.get('/view/latest', 'LoanApplicationSubmissionsController.view')
    Route.get(
      '/history/view/:farmerUserId',
      'LoanApplicationSubmissionsController.viewAllByFarmerUserId'
    )
  }).prefix('/loanapplication')

  // MISC
  Route.group(() => {
    Route.post(
      '/transaction/recomputation',
      'MiscellaneousController.initiateTransactionRecomputationAllFarmer'
    )
    Route.post('/creditscore/sync', 'MiscellaneousController.initiateCreditScoreSyncAllFarmer')
    Route.post(
      '/creditscore/v2/sync/:farmerId',
      'MiscellaneousController.singleCreditScoreRecomputation'
    )
    Route.post('/creditscore/v2/sync', 'MiscellaneousController.allCreditScoreRecomputation')
    Route.post(
      '/loanperiodtracker/sync',
      'MiscellaneousController.initiateLoanPeriodTrackerSyncAllFarmer'
    )
    Route.post(
      '/loanrequirement/initialize',
      'MiscellaneousController.initiateLoanRequirementComputationAllFarmer'
    )
  }).prefix('/misc')

  // TRADING APP
  Route.group(() => {
    // STAFF
    Route.group(() => {
      Route.post('/create', 'TradingAppStaffController.create')
      Route.post('/update', 'TradingAppStaffController.update')
      Route.post('/activate', 'UsersController.activate')
      Route.post('/deactivate', 'UsersController.deactivate')
      Route.get('/viewAll', 'TradingAppStaffController.viewAllByAdmin')
    }).prefix('/staff')

    // STANDARD RATES
    Route.group(() => {
      Route.post('/update', 'TradingAppStandardPricesController.update')
      Route.get('/view', 'TradingAppStandardPricesController.view')
    }).prefix('/standard/price')

    // GROUP RATES
    Route.group(() => {
      Route.post('/create', 'TradingAppGroupPricesController.create')
      Route.post('/update', 'TradingAppGroupPricesController.update')
      Route.get('/view/:id', 'TradingAppGroupPricesController.view')
      Route.get('/viewAll', 'TradingAppGroupPricesController.viewAll')
    }).prefix('/group/price')

    // GROUP USER RATES
    Route.group(() => {
      Route.post('/create', 'TradingAppGroupPriceUsersController.create')
      Route.post('/create/bulk', 'TradingAppGroupPriceUsersController.createBulk')
      Route.post('/update', 'TradingAppGroupPriceUsersController.update')
      Route.post(
        '/delete/:tradingAppGroupPriceUserId',
        'TradingAppGroupPriceUsersController.delete'
      )
      Route.get('/viewAll', 'TradingAppGroupPriceUsersController.viewAll')
      Route.get('/search', 'TradingAppGroupPriceUsersController.search')
    }).prefix('/group/user')

    // NON FARMER
    Route.group(() => {
      Route.post('/update', 'TradingAppNonFarmersController.update')
      Route.post('/activate', 'UsersController.activate')
      Route.post('/deactivate', 'UsersController.deactivate')
      Route.get('/viewAll', 'TradingAppNonFarmersController.viewAllNonFarmer')
      Route.get('/view/:userId', 'TradingAppNonFarmersController.viewNonFarmer')
      Route.get('/view/:userId/audit-logs', 'TradingAppNonFarmersController.viewAuditLog')
    }).prefix('/nonfarmer')
  }).prefix('/tradingapp')

  // CREDITSCORING
  Route.group(() => {
    // CREDIT SCORE CONFIGURATION
    Route.post('/configuration/update', 'CreditScoreConfigurationsController.update')
    Route.get('/configuration/view/:creditScoreGroupId', 'CreditScoreConfigurationsController.view')

    // CREDIT SCORE GROUP
    Route.post('/group/create', 'CreditScoreGroupsController.create')
    Route.post('/group/update', 'CreditScoreGroupsController.update')
    Route.get('/group/viewAll', 'CreditScoreGroupsController.viewAll')

    // FARMER CREDIT SCORE
    Route.get('/farmer/:farmerId/view', 'CreditScoresController.viewCreditScore')
    Route.get(
      '/farmer/:farmerId/history/viewAll',
      'CreditScoresController.viewAllCreditScoreHistory'
    )
    Route.get(
      '/farmer/:farmerId/history/view/:creditScoreHistoryId',
      'CreditScoresController.viewCreditScoreHistory'
    )

    // CREDIT SCORE
    Route.get(
      '/credit-score/:creditScoreGroupId/:farmerId',
      'CreditScoresController.getCreditScore'
    )
    Route.get(
      '/credit-score-recommendation/:creditScoreGroupId/:farmerId',
      'CreditScoresController.getCreditScoreRecommendation'
    )
    Route.get('/credit-score-history/:farmerId', 'CreditScoresController.getCreditScoreHistory')
    Route.get(
      '/credit-score/:credit_score_history_id',
      'CreditScoresController.getCreditScoreHistoryDetail'
    )

    // ALL ROUTES BELOW (DEPRECATED)
    // LOAN WEIGHTAGE
    Route.get('/weightage/:creditScoreGroupId', 'CreditScoresController.getLoanWeightage')
    Route.put('/weightage/:creditScoreGroupId', 'CreditScoresController.updateLoanWeightage')

    // DAG
    Route.post('/dag/farmers/all', 'CreditScoresController.dagAllFarmers')
    Route.post('/dag/farmers/selected', 'CreditScoresController.dagSelectedFarmers')
    Route.get('/dag/status', 'CreditScoresController.getDagRunStatus')

    // RULES
    Route.get('/rules/:id', 'CreditScoresController.getRules')
    Route.put('/update-rules/:id', 'CreditScoresController.updateRules')
  }).prefix('/creditscore')

  // FARM PLAN
  Route.group(() => {
    Route.get('/farmer/:farmerId', 'FarmPlansController.viewAllByFarmer')
  }).prefix('/farmplan')
})
  .prefix('/admin')
  .middleware(['auth:api', 'admin'])

// TRADING POST ENCODER ROUTES
Route.group(() => {
  Route.post('/tradingpost/find', 'TradingPostLogsController.find')
  Route.post('/tradingpost/qr/scan', 'TradingPostLogsController.find_qr')
  Route.post('/tradingpost/view', 'TradingPostLogsController.view')
  Route.get('/tradingpost/viewAll', 'TradingPostLogsController.viewAll')
  Route.get(
    '/tradingpost/transaction/view/:farmerId',
    'TradingPostLogsController.viewAllByFarmerId'
  )
  Route.get('/tradingpost/queue', 'TradingPostLogsController.viewAllQueue')
  Route.post('/tradingpost/update/entry', 'TradingPostLogsController.update_entry')
  Route.post('/tradingpost/update/entry/new', 'TradingPostLogsController.update_new_farmer_entry')
  Route.post('/tradingpost/update/exit', 'TradingPostLogsController.update_exit')

  Route.post('/user/farmer/update', 'UsersController.updateFarmer')
  Route.get('/user/farmers/viewAll', 'UsersController.viewAllFarmersByAdmin')
  Route.get('/user/farmers/viewAllPaginated', 'UsersController.viewAllFarmersByAdminPaginated')
  Route.get('/user/farmer/view/:userId', 'UsersController.viewFarmerByIdByAdmin')

  // OFFLINE SYNC
  Route.post('/sync/sender/tradingpost', 'SyncDataController.syncTradingPostSender')
  Route.post('/sync/sender/user', 'SyncDataController.syncUsersSender')
  Route.post('/sync/sender/crop', 'SyncDataController.syncCropSender')
  Route.post('/sync/sender/crop/price', 'SyncDataController.syncCropPriceSender')
  Route.post('/sync/sender/model/status', 'SyncDataController.getModelSyncStatusSender')

  Route.post('/sync/local/from/cloud', 'SyncDataController.syncFromCloudSender')

  // SCAN
  Route.post('/qr/scanner', 'ApisController.qr_scan')
})
  .prefix('/encoder')
  .middleware(['auth:api', 'encoder'])

// MARKETPLACE OPERATION ROUTES
Route.group(() => {
  // MARKETPLACE
  Route.group(() => {
    // PRODUCT
    Route.group(() => {
      Route.post('/create', 'MarketplaceProductsController.create')
      Route.post('/update', 'MarketplaceProductsController.update')
      Route.get('/viewAll', 'MarketplaceProductsController.viewAll')
      Route.get('/view/:marketplaceProductId', 'MarketplaceProductsController.viewById')
    }).prefix('/product')

    // ORDERS
    Route.group(() => {
      Route.post('/status/update', 'MarketplaceOrdersController.manageOrderStatus')
      Route.get('/dashboard', 'MarketplaceOrdersController.dashboard')
      Route.get('/viewAll', 'MarketplaceOrdersController.viewAll')
      Route.get('/view/:marketplaceOrderId', 'MarketplaceOrdersController.viewById')
    }).prefix('/order')
  }).prefix('/marketplace')

  Route.get('/user/farmers/viewAll', 'UsersController.viewAllFarmersByAdmin')
  Route.get('/user/farmers/viewAllPaginated', 'UsersController.viewAllFarmersByAdminPaginated')
  Route.get('/user/farmer/view/:userId', 'UsersController.viewFarmerByIdByAdmin')
})
  .prefix('/operation')
  .middleware(['auth:api', 'operation'])

// MARKETPLACE SALE ROUTES
Route.group(() => {
  // MARKETPLACE
  Route.group(() => {
    // PRODUCT
    Route.group(() => {
      Route.get('/viewAll', 'MarketplaceProductsController.viewAll')
      Route.get('/view/:marketplaceProductId', 'MarketplaceProductsController.viewById')
    }).prefix('/product')

    // ORDERS
    Route.group(() => {
      Route.post('/create', 'MarketplaceOrdersController.create')
      Route.post('/status/update', 'MarketplaceOrdersController.manageOrderStatus')
      Route.get('/dashboard', 'MarketplaceOrdersController.dashboard')
      Route.get('/viewAll', 'MarketplaceOrdersController.viewAll')
      Route.get('/view/:marketplaceOrderId', 'MarketplaceOrdersController.viewById')
    }).prefix('/order')
  }).prefix('/marketplace')

  Route.get('/user/farmers/viewAll', 'UsersController.viewAllFarmersByAdmin')
  Route.get('/user/farmers/viewAllPaginated', 'UsersController.viewAllFarmersByAdminPaginated')
  Route.get('/user/farmer/view/:userId', 'UsersController.viewFarmerByIdByAdmin')
})
  .prefix('/sale')
  .middleware(['auth:api', 'sale'])

// FINANCE ROUTES
Route.group(() => {
  // TOPUP
  Route.group(() => {
    Route.post('/request', 'TopupRequestsController.create')
    Route.post('/approve', 'TopupRequestsController.approve')
    Route.post('/reject', 'TopupRequestsController.reject')
    Route.get('/viewAll', 'TopupRequestsController.viewAllByFinance')
    Route.get('/view/:topupRequestId', 'TopupRequestsController.viewAllById')
    Route.get('/history/view/:farmerUserId', 'TopupRequestsController.viewAllByFarmerUserId')
  }).prefix('/topup')

  // TOPDOWN
  Route.group(() => {
    Route.post('/request', 'TopdownRequestsController.create')
    Route.post('/approve', 'TopdownRequestsController.approve')
    Route.post('/reject', 'TopdownRequestsController.reject')
    Route.get('/viewAll', 'TopdownRequestsController.viewAllByAdmin')
    Route.get('/view/:topdownRequestId', 'TopdownRequestsController.viewAllById')
    Route.get('/history/view/:farmerUserId', 'TopdownRequestsController.viewAllByFarmerUserId')
  }).prefix('/topdown')

  // LOAN PAYMENT
  Route.group(() => {
    Route.post('/request', 'LoanPaymentRequestsController.create')
    Route.post('/approve', 'LoanPaymentRequestsController.approve')
    Route.post('/reject', 'LoanPaymentRequestsController.reject')
    Route.get('/viewAll', 'LoanPaymentRequestsController.viewAllByFinance')
    Route.get('/view/:loanPaymentRequestId', 'LoanPaymentRequestsController.viewAllById')
    Route.get('/history/view/:farmerUserId', 'LoanPaymentRequestsController.viewAllByFarmerUserId')
    Route.get('/dashboard', 'LoanPaymentRequestsController.dashboard')
  }).prefix('/loanpayment')

  Route.group(() => {
    Route.get('/history/view/:farmerUserId', 'MarketplaceOrdersController.viewAllByFarmerUserId')
  }).prefix('/marketplace')

  Route.group(() => {
    Route.get('/history/view/:farmerUserId', 'TradingPostLogsController.viewAllByFarmerUserId')
  }).prefix('/tradingpost')

  Route.get('/user/farmers/viewAll', 'UsersController.viewAllFarmersByAdmin')
  Route.get('/user/farmers/viewAllPaginated', 'UsersController.viewAllFarmersByAdminPaginated')
  Route.get('/user/farmer/view/:userId', 'UsersController.viewFarmerByIdByAdmin')

  // CREDITSCORING
  Route.group(() => {
    // CREDIT SCORE CONFIGURATION
    Route.post('/configuration/update', 'CreditScoreConfigurationsController.update')
    Route.get('/configuration/view/:creditScoreGroupId', 'CreditScoreConfigurationsController.view')

    // CREDIT SCORE GROUP
    Route.post('/group/create', 'CreditScoreGroupsController.create')
    Route.post('/group/update', 'CreditScoreGroupsController.update')
    Route.get('/group/viewAll', 'CreditScoreGroupsController.viewAll')

    // FARMER CREDIT SCORE
    Route.get('/farmer/:farmerId/view', 'CreditScoresController.viewCreditScore')
    Route.get(
      '/farmer/:farmerId/history/viewAll',
      'CreditScoresController.viewAllCreditScoreHistory'
    )
    Route.get(
      '/farmer/:farmerId/history/view/:creditScoreHistoryId',
      'CreditScoresController.viewCreditScoreHistory'
    )

    // CREDIT SCORE
    Route.get(
      '/credit-score/:creditScoreGroupId/:farmerId',
      'CreditScoresController.getCreditScore'
    )
    Route.get(
      '/credit-score-recommendation/:creditScoreGroupId/:farmerId',
      'CreditScoresController.getCreditScoreRecommendation'
    )
    Route.get('/credit-score-history/:farmerId', 'CreditScoresController.getCreditScoreHistory')
    Route.get(
      '/credit-score/:credit_score_history_id',
      'CreditScoresController.getCreditScoreHistoryDetail'
    )

    // ALL ROUTES BELOW (DEPRECATED)
    // LOAN WEIGHTAGE
    Route.get('/weightage/:creditScoreGroupId', 'CreditScoresController.getLoanWeightage')
    Route.put('/weightage/:creditScoreGroupId', 'CreditScoresController.updateLoanWeightage')

    // DAG
    Route.post('/dag/farmers/all', 'CreditScoresController.dagAllFarmers')
    Route.post('/dag/farmers/selected', 'CreditScoresController.dagSelectedFarmers')
    Route.get('/dag/status', 'CreditScoresController.getDagRunStatus')

    // RULES
    Route.get('/rules/:id', 'CreditScoresController.getRules')
    Route.put('/update-rules/:id', 'CreditScoresController.updateRules')
  }).prefix('/creditscore')

  // LOANAPPLICATIONSUBMISSION
  Route.group(() => {
    Route.post('/submit', 'LoanApplicationSubmissionsController.create')
    Route.get('/view/latest', 'LoanApplicationSubmissionsController.view')
    Route.get(
      '/history/view/:farmerUserId',
      'LoanApplicationSubmissionsController.viewAllByFarmerUserId'
    )
  }).prefix('/loanapplication')

  // NON LOAN HOLDER
  Route.get('/user/farmers/nonloanholder', 'MembersController.nonLoanHolderViewAll')
  Route.get('/user/farmers/nonloanholder/dashboard', 'MembersController.nonLoanHolderDashboard')

  // LOAN HOLDER
  Route.get('/user/farmers/loanholder', 'MembersController.loanHolderViewAll')
  Route.get('/user/farmers/loanholder/dashboard', 'MembersController.loanHolderDashboard')

  // LOAN APPLICANT
  Route.get('/user/farmers/loanapplicant', 'MembersController.loanApplicantHolderViewAll')
  Route.get(
    '/user/farmers/loanapplicant/dashboard',
    'MembersController.loanApplicantHolderDashboard'
  )
  Route.get(
    '/user/farmers/loanapplicant/:loanApplicationSubmissionId/view',
    'MembersController.loanApplicantHolderViewDetail'
  )
  Route.get(
    '/user/farmers/approved/loanapplicant',
    'MembersController.approvedLoanApplicantHolderUserViewAll'
  )
  // LOAN APPLICANT ACTIONS
  Route.post('/user/farmers/loanapplicant/approve', 'MembersController.approveLoanApplicant')
  Route.post('/user/farmers/loanapplicant/reject', 'MembersController.rejectLoanApplicant')
})
  .prefix('/finance')
  .middleware(['auth:api', 'finance'])

// AGRONOMIST ROUTES
Route.group(() => {
  // FARM PLAN
  Route.group(() => {
    Route.post('/create', 'FarmPlansController.create')
    Route.post('/update', 'FarmPlansController.update')
    Route.get('/viewAll', 'FarmPlansController.viewAll')
    Route.get('/view/:farmPlanId', 'FarmPlansController.view')

    Route.get('/farmer/search', 'FarmPlansController.searchFarmer')
    Route.get('/:farmPlanId/logs', 'FarmPlansController.viewLogs')
    Route.post('/subitem/update', 'FarmPlansController.updateSubItem')
    Route.post('/subitem/remove', 'FarmPlansController.removedSubItem')
    Route.post('/subitem/add', 'FarmPlansController.addSubItem')
  }).prefix('/farmplan')

  // FARM PLAN MASTER TEMPLATE
  Route.group(() => {
    Route.post('/create', 'FarmPlanTemplatesController.create')
    Route.post('/update', 'FarmPlanTemplatesController.update')
    Route.get('/viewAll', 'FarmPlanTemplatesController.viewAll')
    Route.get('/view/:farmPlanTemplateId', 'FarmPlanTemplatesController.view')

    Route.get('/:farmPlanTemplateId/logs', 'FarmPlanTemplatesController.viewLogs')
    Route.post('/subitem/update', 'FarmPlanTemplatesController.updateSubItem')
    Route.post('/subitem/remove', 'FarmPlanTemplatesController.removedSubItem')
    Route.post('/subitem/add', 'FarmPlanTemplatesController.addSubItem')
  }).prefix('/farmplan/template')
})
  .prefix('/agronomist')
  .middleware(['auth:api', 'agronomist'])

// FIELD RELATION OFFICER ROUTES
Route.group(() => {
  Route.post('/crops/create/single', 'CropsController.create_single')

  Route.get('/farmer/view/:userId', 'FieldRelationOfficersController.viewFarmerByIdByFro')
  Route.get('/farmer/viewAll', 'FieldRelationOfficersController.viewAllFroRegisteredAccounts')
  Route.post('/farmer/register', 'FieldRelationOfficersController.registerFarmer')
  Route.post('/farmer/update', 'FieldRelationOfficersController.updateFarmer')
})
  .prefix('/fro')
  .middleware(['auth:api', 'fro'])

// AUTHENTICATED FARMER ROUTES
Route.group(() => {
  Route.group(() => {
    // PRODUCT
    Route.group(() => {
      Route.get('/viewAll', 'MarketplaceProductsController.viewAllPublic')
      Route.get('/view/:marketplaceProductId', 'MarketplaceProductsController.viewByIdPublic')
    }).prefix('/product')
  }).prefix('/marketplace')

  Route.post('/account/update/password', 'UsersController.account_update_password')
}).middleware(['auth:api'])

// OFFLINE MODE
Route.post('/sync/receiver/tradingpost', 'SyncDataController.syncTradingPostReceiver')
Route.post('/sync/receiver/tradingpost/single', 'SyncDataController.syncSingleTradingPostReceiver')

Route.post('/sync/receiver/user', 'SyncDataController.syncUsersReceiver')
Route.post('/sync/receiver/user/validate', 'SyncDataController.syncUsersValidateReceiver')

Route.post('/sync/receiver/model/status', 'SyncDataController.getModelSyncStatusReceiver')

Route.post('/sync/receiver/crop', 'SyncDataController.syncCropReceiver')
Route.post('/sync/receiver/crop/validate', 'SyncDataController.syncCropValidateReceiver')

Route.post('/sync/receiver/crop/price', 'SyncDataController.syncCropPriceReceiver')
Route.post('/sync/receiver/crop/price/validate', 'SyncDataController.syncCropPriceValidateReceiver')

// SHURU API REQUEST
Route.get('/loan/transactions/before', 'LoanPeriodTrackersController.viewBeforeLoanTransaction')
Route.get('/loan/transactions/during', 'LoanPeriodTrackersController.viewDuringLoanTransaction')
Route.post('/creditscore/callback', 'MiscellaneousController.creditScoreCallback')
